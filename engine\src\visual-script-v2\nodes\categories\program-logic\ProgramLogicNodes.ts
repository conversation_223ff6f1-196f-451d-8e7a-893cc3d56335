/**
 * 第一批次：程序逻辑控制基础节点 (001-050)
 * 实现基础流程控制、高级流程控制和异常处理节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 001 - 开始节点 (Start Node)
 * 程序执行入口点
 */
export class StartNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/start', '开始节点', NodeCategory.FLOW_CONTROL);
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '程序开始执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    context.log('info', '程序开始执行');
    context.setOutputValue('exec', true);
  }
}

/**
 * 002 - 结束节点 (End Node)
 * 程序执行结束点
 */
export class EndNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/end', '结束节点', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发程序结束'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    context.log('info', '程序执行结束');
  }
}

/**
 * 003 - 序列执行 (Sequence)
 * 按顺序执行多个节点
 */
export class SequenceNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/sequence', '序列执行', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发序列执行'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'exec1',
        label: '执行1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个执行输出'
      },
      {
        name: 'exec2',
        label: '执行2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个执行输出'
      },
      {
        name: 'exec3',
        label: '执行3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个执行输出'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    // 按顺序触发输出
    context.setOutputValue('exec1', true);
    await new Promise(resolve => setTimeout(resolve, 0)); // 让出执行权
    context.setOutputValue('exec2', true);
    await new Promise(resolve => setTimeout(resolve, 0));
    context.setOutputValue('exec3', true);
  }
}

/**
 * 004 - 条件分支 (Branch)
 * 基于条件的分支执行
 */
export class BranchNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/branch', '条件分支', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发条件判断'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '判断条件'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'true',
        label: '真',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为真时执行'
      },
      {
        name: 'false',
        label: '假',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件为假时执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const condition = context.getInputValue('condition');
    if (condition) {
      context.setOutputValue('true', true);
    } else {
      context.setOutputValue('false', true);
    }
  }
}

/**
 * 005 - 多路分支 (Switch)
 * 多条件分支选择
 */
export class SwitchNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/switch', '多路分支', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发分支选择'
      },
      {
        name: 'selection',
        label: '选择',
        type: DataType.NUMBER,
        direction: 'input',
        required: true,
        defaultValue: 0,
        description: '选择的分支索引'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'case0',
        label: '分支0',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '选择为0时执行'
      },
      {
        name: 'case1',
        label: '分支1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '选择为1时执行'
      },
      {
        name: 'case2',
        label: '分支2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '选择为2时执行'
      },
      {
        name: 'default',
        label: '默认',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '没有匹配时执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const selection = context.getInputValue('selection');
    const index = Math.floor(selection);
    
    switch (index) {
      case 0:
        context.setOutputValue('case0', true);
        break;
      case 1:
        context.setOutputValue('case1', true);
        break;
      case 2:
        context.setOutputValue('case2', true);
        break;
      default:
        context.setOutputValue('default', true);
        break;
    }
  }
}

/**
 * 006 - 循环控制 (Loop Control)
 * 循环执行控制
 */
export class LoopControlNode extends BaseNode {
  private isLooping = false;
  private currentIteration = 0;

  constructor(id?: string) {
    super(id, 'program-logic/loop-control', '循环控制', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'start',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '开始循环'
      },
      {
        name: 'stop',
        label: '停止',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '停止循环'
      },
      {
        name: 'maxIterations',
        label: '最大迭代次数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '最大循环次数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'iteration',
        label: '迭代次数',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前迭代次数'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const startTrigger = context.getInputValue('start');
    const stopTrigger = context.getInputValue('stop');
    const maxIterations = context.getInputValue('maxIterations') || 10;

    if (stopTrigger) {
      this.isLooping = false;
      return;
    }

    if (startTrigger && !this.isLooping) {
      this.isLooping = true;
      this.currentIteration = 0;
    }

    if (this.isLooping) {
      if (this.currentIteration < maxIterations) {
        context.setOutputValue('iteration', this.currentIteration);
        context.setOutputValue('loopBody', true);
        this.currentIteration++;
      } else {
        this.isLooping = false;
        context.setOutputValue('completed', true);
      }
    }
  }
}

/**
 * 007 - For循环 (For Loop)
 * 指定次数的循环
 */
export class ForLoopNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/for-loop', 'For循环', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始循环'
      },
      {
        name: 'startIndex',
        label: '起始索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 0,
        description: '循环起始索引'
      },
      {
        name: 'endIndex',
        label: '结束索引',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '循环结束索引'
      },
      {
        name: 'step',
        label: '步长',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '循环步长'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'index',
        label: '当前索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前循环索引'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成时执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const startIndex = context.getInputValue('startIndex') || 0;
    const endIndex = context.getInputValue('endIndex') || 10;
    const step = context.getInputValue('step') || 1;

    for (let i = startIndex; i < endIndex; i += step) {
      context.setOutputValue('index', i);
      context.setOutputValue('loopBody', true);
      await new Promise(resolve => setTimeout(resolve, 0)); // 让出执行权
    }

    context.setOutputValue('completed', true);
  }
}

/**
 * 008 - While循环 (While Loop)
 * 条件循环
 */
export class WhileLoopNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/while-loop', 'While循环', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始循环'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '循环条件'
      },
      {
        name: 'maxIterations',
        label: '最大迭代次数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1000,
        description: '防止无限循环的最大迭代次数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'loopBody',
        label: '循环体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次循环执行'
      },
      {
        name: 'iteration',
        label: '迭代次数',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前迭代次数'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '循环完成时执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const maxIterations = context.getInputValue('maxIterations') || 1000;
    let iteration = 0;

    while (context.getInputValue('condition') && iteration < maxIterations) {
      context.setOutputValue('iteration', iteration);
      context.setOutputValue('loopBody', true);
      iteration++;
      await new Promise(resolve => setTimeout(resolve, 0)); // 让出执行权
    }

    context.setOutputValue('completed', true);
  }
}

/**
 * 009 - 延迟执行 (Delay)
 * 延迟指定时间后执行
 */
export class DelayNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/delay', '延迟执行', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始延迟'
      },
      {
        name: 'duration',
        label: '延迟时间',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1.0,
        description: '延迟时间（秒）'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '延迟完成后执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const duration = context.getInputValue('duration') || 1.0;
    const delayMs = duration * 1000;

    await new Promise(resolve => setTimeout(resolve, delayMs));
    context.setOutputValue('completed', true);
  }
}

/**
 * 010 - 门控制 (Gate)
 * 控制信号的通过
 */
export class GateNode extends BaseNode {
  private isOpen = false;

  constructor(id?: string) {
    super(id, 'program-logic/gate', '门控制', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'enter',
        label: '进入',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '尝试通过门的信号'
      },
      {
        name: 'open',
        label: '打开',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '打开门'
      },
      {
        name: 'close',
        label: '关闭',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '关闭门'
      },
      {
        name: 'toggle',
        label: '切换',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '切换门状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'exit',
        label: '退出',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '信号通过门后的输出'
      },
      {
        name: 'isOpen',
        label: '是否打开',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '门的当前状态'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const enterTrigger = context.getInputValue('enter');
    const openTrigger = context.getInputValue('open');
    const closeTrigger = context.getInputValue('close');
    const toggleTrigger = context.getInputValue('toggle');

    if (openTrigger) {
      this.isOpen = true;
    }
    if (closeTrigger) {
      this.isOpen = false;
    }
    if (toggleTrigger) {
      this.isOpen = !this.isOpen;
    }

    context.setOutputValue('isOpen', this.isOpen);

    if (enterTrigger && this.isOpen) {
      context.setOutputValue('exit', true);
    }
  }
}

/**
 * 011 - 执行一次 (Do Once)
 * 只执行一次，后续调用被忽略
 */
export class DoOnceNode extends BaseNode {
  private hasExecuted = false;

  constructor(id?: string) {
    super(id, 'program-logic/do-once', '执行一次', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发执行'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置执行状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一次执行时触发'
      },
      {
        name: 'hasExecuted',
        label: '已执行',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否已经执行过'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const execTrigger = context.getInputValue('exec');
    const resetTrigger = context.getInputValue('reset');

    if (resetTrigger) {
      this.hasExecuted = false;
    }

    context.setOutputValue('hasExecuted', this.hasExecuted);

    if (execTrigger && !this.hasExecuted) {
      this.hasExecuted = true;
      context.setOutputValue('completed', true);
    }
  }
}

/**
 * 012 - 多重门 (Multi Gate)
 * 循环输出到多个端口
 */
export class MultiGateNode extends BaseNode {
  private currentIndex = 0;

  constructor(id?: string) {
    super(id, 'program-logic/multi-gate', '多重门', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发输出'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置到第一个输出'
      },
      {
        name: 'loop',
        label: '循环',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: false,
        defaultValue: true,
        description: '是否循环输出'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'out0',
        label: '输出0',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个输出'
      },
      {
        name: 'out1',
        label: '输出1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个输出'
      },
      {
        name: 'out2',
        label: '输出2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个输出'
      },
      {
        name: 'out3',
        label: '输出3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第四个输出'
      },
      {
        name: 'currentIndex',
        label: '当前索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前输出索引'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const execTrigger = context.getInputValue('exec');
    const resetTrigger = context.getInputValue('reset');
    const loop = context.getInputValue('loop') !== false;

    if (resetTrigger) {
      this.currentIndex = 0;
    }

    if (execTrigger) {
      const outputName = `out${this.currentIndex}`;
      context.setOutputValue(outputName, true);
      context.setOutputValue('currentIndex', this.currentIndex);

      this.currentIndex++;
      if (this.currentIndex >= 4) {
        if (loop) {
          this.currentIndex = 0;
        } else {
          this.currentIndex = 3; // 停留在最后一个输出
        }
      }
    }
  }
}
