/**
 * 第一批次：程序逻辑控制基础节点 (043-050)
 * 完成最后的设计模式和控制节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 043 - 装饰器模式 (Decorator Pattern)
 * 装饰器模式实现
 */
export class DecoratorPatternNode extends BaseNode {
  private decorators: Array<{ name: string; data: any }> = [];

  constructor(id?: string) {
    super(id, 'program-logic/decorator-pattern', '装饰器模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '执行装饰后的对象'
      },
      {
        name: 'addDecorator',
        label: '添加装饰器',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加装饰器'
      },
      {
        name: 'removeDecorator',
        label: '移除装饰器',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '移除装饰器'
      },
      {
        name: 'decoratorName',
        label: '装饰器名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '装饰器名称'
      },
      {
        name: 'decoratorData',
        label: '装饰器数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '装饰器数据'
      },
      {
        name: 'baseObject',
        label: '基础对象',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要装饰的基础对象'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'decoratedObject',
        label: '装饰后对象',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '装饰后的对象'
      },
      {
        name: 'decoratorCount',
        label: '装饰器数量',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前装饰器数量'
      },
      {
        name: 'decorators',
        label: '装饰器列表',
        type: DataType.ARRAY,
        direction: 'output',
        required: false,
        description: '所有装饰器列表'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const executeTrigger = context.getInputValue('execute');
    const addDecoratorTrigger = context.getInputValue('addDecorator');
    const removeDecoratorTrigger = context.getInputValue('removeDecorator');
    const decoratorName = context.getInputValue('decoratorName');
    const decoratorData = context.getInputValue('decoratorData');
    const baseObject = context.getInputValue('baseObject');

    if (addDecoratorTrigger && decoratorName) {
      this.decorators.push({ name: decoratorName, data: decoratorData });
    }

    if (removeDecoratorTrigger && decoratorName) {
      this.decorators = this.decorators.filter(d => d.name !== decoratorName);
    }

    context.setOutputValue('decoratorCount', this.decorators.length);
    context.setOutputValue('decorators', [...this.decorators]);

    if (executeTrigger) {
      // 应用所有装饰器到基础对象
      let decoratedObject = baseObject || {};
      
      for (const decorator of this.decorators) {
        decoratedObject = {
          ...decoratedObject,
          [`decorated_${decorator.name}`]: decorator.data,
          decorators: this.decorators.map(d => d.name)
        };
      }

      context.setOutputValue('decoratedObject', decoratedObject);
    }
  }
}

/**
 * 044 - 适配器模式 (Adapter Pattern)
 * 适配器模式实现
 */
export class AdapterPatternNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/adapter-pattern', '适配器模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'adapt',
        label: '适配',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '执行适配转换'
      },
      {
        name: 'sourceObject',
        label: '源对象',
        type: DataType.ANY,
        direction: 'input',
        required: true,
        description: '要适配的源对象'
      },
      {
        name: 'targetInterface',
        label: '目标接口',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'default',
        description: '目标接口类型'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'adaptedObject',
        label: '适配后对象',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '适配后的对象'
      },
      {
        name: 'success',
        label: '成功',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '适配是否成功'
      },
      {
        name: 'error',
        label: '错误',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '适配错误信息'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const sourceObject = context.getInputValue('sourceObject');
    const targetInterface = context.getInputValue('targetInterface') || 'default';

    try {
      // 模拟适配器转换
      const adaptedObject = {
        originalObject: sourceObject,
        adaptedTo: targetInterface,
        adaptedAt: new Date().toISOString(),
        // 根据目标接口进行适配转换
        adaptedData: this.performAdaptation(sourceObject, targetInterface)
      };

      context.setOutputValue('adaptedObject', adaptedObject);
      context.setOutputValue('success', true);
      context.log('info', `适配器转换成功: ${targetInterface}`, adaptedObject);
    } catch (error) {
      context.setOutputValue('success', false);
      context.setOutputValue('error', error instanceof Error ? error.message : String(error));
    }
  }

  private performAdaptation(sourceObject: any, targetInterface: string): any {
    // 简化的适配逻辑
    switch (targetInterface) {
      case 'array':
        return Array.isArray(sourceObject) ? sourceObject : [sourceObject];
      case 'string':
        return typeof sourceObject === 'string' ? sourceObject : JSON.stringify(sourceObject);
      case 'number':
        return typeof sourceObject === 'number' ? sourceObject : parseFloat(String(sourceObject)) || 0;
      case 'boolean':
        return Boolean(sourceObject);
      default:
        return sourceObject;
    }
  }
}

/**
 * 045 - 代理模式 (Proxy Pattern)
 * 代理模式实现
 */
export class ProxyPatternNode extends BaseNode {
  private accessLog: Array<{ action: string; timestamp: number; data?: any }> = [];

  constructor(id?: string) {
    super(id, 'program-logic/proxy-pattern', '代理模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'access',
        label: '访问',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '通过代理访问对象'
      },
      {
        name: 'targetObject',
        label: '目标对象',
        type: DataType.ANY,
        direction: 'input',
        required: true,
        description: '被代理的目标对象'
      },
      {
        name: 'operation',
        label: '操作',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'read',
        description: '要执行的操作'
      },
      {
        name: 'operationData',
        label: '操作数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '操作相关数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '代理操作结果'
      },
      {
        name: 'accessGranted',
        label: '访问允许',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否允许访问'
      },
      {
        name: 'accessLog',
        label: '访问日志',
        type: DataType.ARRAY,
        direction: 'output',
        required: false,
        description: '访问日志记录'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const targetObject = context.getInputValue('targetObject');
    const operation = context.getInputValue('operation') || 'read';
    const operationData = context.getInputValue('operationData');

    // 记录访问日志
    const logEntry = {
      action: operation,
      timestamp: Date.now(),
      data: operationData
    };
    this.accessLog.push(logEntry);

    // 简化的访问控制逻辑
    const accessGranted = this.checkAccess(operation);
    context.setOutputValue('accessGranted', accessGranted);

    if (accessGranted) {
      // 执行代理操作
      const result = this.performProxyOperation(targetObject, operation, operationData);
      context.setOutputValue('result', result);
    } else {
      context.log('warn', `代理访问被拒绝: ${operation}`);
    }

    context.setOutputValue('accessLog', [...this.accessLog]);
  }

  private checkAccess(operation: string): boolean {
    // 简化的访问控制逻辑
    const allowedOperations = ['read', 'write', 'execute'];
    return allowedOperations.includes(operation);
  }

  private performProxyOperation(targetObject: any, operation: string, data?: any): any {
    // 简化的代理操作逻辑
    switch (operation) {
      case 'read':
        return targetObject;
      case 'write':
        return { ...targetObject, ...data };
      case 'execute':
        return `执行操作: ${JSON.stringify(targetObject)}`;
      default:
        return targetObject;
    }
  }
}

/**
 * 046 - 模板方法模式 (Template Method Pattern)
 * 模板方法模式实现
 */
export class TemplateMethodPatternNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/template-method-pattern', '模板方法模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '执行模板方法'
      },
      {
        name: 'templateType',
        label: '模板类型',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'default',
        description: '模板类型'
      },
      {
        name: 'inputData',
        label: '输入数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '模板处理的输入数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'step1',
        label: '步骤1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '模板步骤1'
      },
      {
        name: 'step2',
        label: '步骤2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '模板步骤2'
      },
      {
        name: 'step3',
        label: '步骤3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '模板步骤3'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '模板执行完成'
      },
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '模板执行结果'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const templateType = context.getInputValue('templateType') || 'default';
    const inputData = context.getInputValue('inputData');

    // 执行模板方法的固定步骤
    context.setOutputValue('step1', true);
    await new Promise(resolve => setTimeout(resolve, 10));

    context.setOutputValue('step2', true);
    await new Promise(resolve => setTimeout(resolve, 10));

    context.setOutputValue('step3', true);
    await new Promise(resolve => setTimeout(resolve, 10));

    // 根据模板类型生成不同的结果
    const result = this.generateResult(templateType, inputData);
    context.setOutputValue('result', result);
    context.setOutputValue('completed', true);

    context.log('info', `模板方法执行完成: ${templateType}`, result);
  }

  private generateResult(templateType: string, inputData: any): any {
    return {
      templateType,
      processedData: `${templateType}_processed_${inputData}`,
      timestamp: new Date().toISOString(),
      steps: ['step1', 'step2', 'step3']
    };
  }
}

/**
 * 047 - 责任链模式 (Chain of Responsibility Pattern)
 * 责任链模式实现
 */
export class ChainOfResponsibilityNode extends BaseNode {
  private handlers: Array<{ name: string; priority: number; condition: any }> = [];

  constructor(id?: string) {
    super(id, 'program-logic/chain-of-responsibility', '责任链模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'process',
        label: '处理',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始责任链处理'
      },
      {
        name: 'addHandler',
        label: '添加处理器',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加处理器到链中'
      },
      {
        name: 'request',
        label: '请求',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要处理的请求'
      },
      {
        name: 'handlerName',
        label: '处理器名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '处理器名称'
      },
      {
        name: 'handlerPriority',
        label: '处理器优先级',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1,
        description: '处理器优先级'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'handler1',
        label: '处理器1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个处理器'
      },
      {
        name: 'handler2',
        label: '处理器2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个处理器'
      },
      {
        name: 'handler3',
        label: '处理器3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个处理器'
      },
      {
        name: 'unhandled',
        label: '未处理',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '没有处理器能处理请求'
      },
      {
        name: 'currentHandler',
        label: '当前处理器',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前处理的处理器名'
      },
      {
        name: 'request',
        label: '请求',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '传递的请求数据'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const processTrigger = context.getInputValue('process');
    const addHandlerTrigger = context.getInputValue('addHandler');
    const request = context.getInputValue('request');
    const handlerName = context.getInputValue('handlerName');
    const handlerPriority = context.getInputValue('handlerPriority') || 1;

    if (addHandlerTrigger && handlerName) {
      this.handlers.push({
        name: handlerName,
        priority: handlerPriority,
        condition: null
      });
      // 按优先级排序
      this.handlers.sort((a, b) => b.priority - a.priority);
    }

    if (processTrigger) {
      context.setOutputValue('request', request);

      // 简化的责任链处理逻辑
      const requestType = typeof request === 'string' ? request : 'default';

      if (requestType.includes('1') || this.handlers.length === 0) {
        context.setOutputValue('handler1', true);
        context.setOutputValue('currentHandler', 'handler1');
      } else if (requestType.includes('2')) {
        context.setOutputValue('handler2', true);
        context.setOutputValue('currentHandler', 'handler2');
      } else if (requestType.includes('3')) {
        context.setOutputValue('handler3', true);
        context.setOutputValue('currentHandler', 'handler3');
      } else {
        context.setOutputValue('unhandled', true);
        context.setOutputValue('currentHandler', 'none');
      }
    }
  }
}

/**
 * 048 - 状态模式 (State Pattern)
 * 状态模式实现
 */
export class StatePatternNode extends BaseNode {
  private currentState = 'initial';
  private stateData = new Map<string, any>();

  constructor(id?: string) {
    super(id, 'program-logic/state-pattern', '状态模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'handleRequest',
        label: '处理请求',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '处理状态请求'
      },
      {
        name: 'changeState',
        label: '改变状态',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '改变当前状态'
      },
      {
        name: 'newState',
        label: '新状态',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要切换到的新状态'
      },
      {
        name: 'requestData',
        label: '请求数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '请求相关数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'stateA',
        label: '状态A',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '状态A的处理'
      },
      {
        name: 'stateB',
        label: '状态B',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '状态B的处理'
      },
      {
        name: 'stateC',
        label: '状态C',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '状态C的处理'
      },
      {
        name: 'stateChanged',
        label: '状态已改变',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '状态改变时触发'
      },
      {
        name: 'currentState',
        label: '当前状态',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前状态名'
      },
      {
        name: 'previousState',
        label: '前一状态',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '前一个状态名'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const handleRequestTrigger = context.getInputValue('handleRequest');
    const changeStateTrigger = context.getInputValue('changeState');
    const newState = context.getInputValue('newState');
    const requestData = context.getInputValue('requestData');

    if (changeStateTrigger && newState && newState !== this.currentState) {
      const previousState = this.currentState;
      this.currentState = newState;

      context.setOutputValue('stateChanged', true);
      context.setOutputValue('previousState', previousState);
      context.log('info', `状态改变: ${previousState} -> ${newState}`);
    }

    context.setOutputValue('currentState', this.currentState);

    if (handleRequestTrigger) {
      // 根据当前状态处理请求
      switch (this.currentState) {
        case 'A':
        case 'stateA':
          context.setOutputValue('stateA', true);
          break;
        case 'B':
        case 'stateB':
          context.setOutputValue('stateB', true);
          break;
        case 'C':
        case 'stateC':
          context.setOutputValue('stateC', true);
          break;
        default:
          context.setOutputValue('stateA', true); // 默认状态
          break;
      }
    }
  }
}

/**
 * 049 - 访问者模式 (Visitor Pattern)
 * 访问者模式实现
 */
export class VisitorPatternNode extends BaseNode {
  private elements: Array<{ type: string; data: any }> = [];

  constructor(id?: string) {
    super(id, 'program-logic/visitor-pattern', '访问者模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'visit',
        label: '访问',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '开始访问元素'
      },
      {
        name: 'addElement',
        label: '添加元素',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加要访问的元素'
      },
      {
        name: 'visitorType',
        label: '访问者类型',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'default',
        description: '访问者类型'
      },
      {
        name: 'elementType',
        label: '元素类型',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要添加的元素类型'
      },
      {
        name: 'elementData',
        label: '元素数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '元素数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'visitElement',
        label: '访问元素',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '访问每个元素时触发'
      },
      {
        name: 'visitCompleted',
        label: '访问完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '所有元素访问完成'
      },
      {
        name: 'currentElement',
        label: '当前元素',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '当前访问的元素'
      },
      {
        name: 'visitorType',
        label: '访问者类型',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前访问者类型'
      },
      {
        name: 'elementCount',
        label: '元素数量',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '总元素数量'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const visitTrigger = context.getInputValue('visit');
    const addElementTrigger = context.getInputValue('addElement');
    const visitorType = context.getInputValue('visitorType') || 'default';
    const elementType = context.getInputValue('elementType');
    const elementData = context.getInputValue('elementData');

    if (addElementTrigger && elementType) {
      this.elements.push({ type: elementType, data: elementData });
    }

    context.setOutputValue('elementCount', this.elements.length);
    context.setOutputValue('visitorType', visitorType);

    if (visitTrigger) {
      // 访问所有元素
      for (const element of this.elements) {
        context.setOutputValue('currentElement', element);
        context.setOutputValue('visitElement', true);
        await new Promise(resolve => setTimeout(resolve, 10)); // 短暂延迟
      }

      context.setOutputValue('visitCompleted', true);
      context.log('info', `访问者 ${visitorType} 完成访问 ${this.elements.length} 个元素`);
    }
  }
}

/**
 * 050 - 中介者模式 (Mediator Pattern)
 * 中介者模式实现
 */
export class MediatorPatternNode extends BaseNode {
  private colleagues: Map<string, any> = new Map();
  private messageHistory: Array<{ from: string; to: string; message: any; timestamp: number }> = [];

  constructor(id?: string) {
    super(id, 'program-logic/mediator-pattern', '中介者模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'sendMessage',
        label: '发送消息',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '通过中介者发送消息'
      },
      {
        name: 'registerColleague',
        label: '注册同事',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '注册同事对象'
      },
      {
        name: 'fromColleague',
        label: '发送者',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '消息发送者'
      },
      {
        name: 'toColleague',
        label: '接收者',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '消息接收者'
      },
      {
        name: 'message',
        label: '消息',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要发送的消息'
      },
      {
        name: 'colleagueId',
        label: '同事ID',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要注册的同事ID'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'messageDelivered',
        label: '消息已送达',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '消息送达时触发'
      },
      {
        name: 'broadcast',
        label: '广播',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '广播消息给所有同事'
      },
      {
        name: 'deliveredMessage',
        label: '送达消息',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '送达的消息对象'
      },
      {
        name: 'colleagueCount',
        label: '同事数量',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '注册的同事数量'
      },
      {
        name: 'messageHistory',
        label: '消息历史',
        type: DataType.ARRAY,
        direction: 'output',
        required: false,
        description: '消息历史记录'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const sendMessageTrigger = context.getInputValue('sendMessage');
    const registerColleagueTrigger = context.getInputValue('registerColleague');
    const fromColleague = context.getInputValue('fromColleague');
    const toColleague = context.getInputValue('toColleague');
    const message = context.getInputValue('message');
    const colleagueId = context.getInputValue('colleagueId');

    if (registerColleagueTrigger && colleagueId) {
      this.colleagues.set(colleagueId, { id: colleagueId, registeredAt: Date.now() });
      context.log('info', `注册同事: ${colleagueId}`);
    }

    context.setOutputValue('colleagueCount', this.colleagues.size);

    if (sendMessageTrigger && fromColleague && message) {
      const messageObj = {
        from: fromColleague,
        to: toColleague || 'all',
        message: message,
        timestamp: Date.now()
      };

      this.messageHistory.push(messageObj);
      context.setOutputValue('deliveredMessage', messageObj);

      if (toColleague && toColleague !== 'all') {
        // 点对点消息
        context.setOutputValue('messageDelivered', true);
      } else {
        // 广播消息
        context.setOutputValue('broadcast', true);
      }

      context.log('info', `中介者转发消息: ${fromColleague} -> ${toColleague || 'all'}`, message);
    }

    context.setOutputValue('messageHistory', [...this.messageHistory]);
  }
}
