/**
 * 实体组件系统节点 (121-140)
 * 实现实体创建、组件管理、实体查询等ECS功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 121 - 实体创建节点
 * 创建新的实体
 */
export class EntityCreateNode extends BaseNode {
  constructor() {
    super('EntityCreate', '实体创建', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('world', '世界实例', DataType.OBJECT);
    this.addInputPort('entityName', '实体名称', DataType.STRING);
    this.addInputPort('components', '初始组件', DataType.ARRAY);
    this.addInputPort('parent', '父实体', DataType.OBJECT);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('entity', '实体', DataType.OBJECT);
    this.addOutputPort('created', '已创建', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const world = this.getInputValue('world');
    const entityName = this.getInputValue('entityName') || `entity_${Date.now()}`;
    const components = this.getInputValue('components') || [];
    const parent = this.getInputValue('parent');

    if (!world) {
      throw new Error('需要世界实例');
    }

    try {
      // 生成实体ID
      const entityId = ++world.entityIdCounter;

      // 创建实体
      const entity = {
        id: entityId,
        name: entityName,
        components: new Map(),
        children: new Set(),
        parent: parent ? parent.id : null,
        isActive: true,
        createdAt: Date.now()
      };

      // 添加初始组件
      for (const componentData of components) {
        await this.addComponent(entity, componentData);
      }

      // 设置父子关系
      if (parent) {
        parent.children.add(entityId);
        entity.parent = parent.id;
      }

      // 注册到世界
      world.entities.set(entityId, entity);

      this.setOutputValue('entity', entity);

      console.log(`实体已创建: ${entityName} (ID: ${entityId})`);
      await this.triggerOutput(context, 'created');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('实体创建失败:', error);
      throw error;
    }
  }

  private async addComponent(entity: any, componentData: any): Promise<void> {
    if (componentData && componentData.type) {
      const component = {
        type: componentData.type,
        data: componentData.data || {},
        isActive: true,
        addedAt: Date.now()
      };

      entity.components.set(componentData.type, component);
    }
  }
}

/**
 * 122 - 实体销毁节点
 * 销毁实体及其组件
 */
export class EntityDestroyNode extends BaseNode {
  constructor() {
    super('EntityDestroy', '实体销毁', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('world', '世界实例', DataType.OBJECT);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('destroyChildren', '销毁子实体', DataType.BOOLEAN);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('destroyed', '已销毁', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const world = this.getInputValue('world');
    const entity = this.getInputValue('entity');
    const destroyChildren = this.getInputValue('destroyChildren') !== false;

    if (!world || !entity) {
      throw new Error('需要世界实例和实体');
    }

    try {
      // 销毁子实体
      if (destroyChildren && entity.children.size > 0) {
        for (const childId of entity.children) {
          const childEntity = world.entities.get(childId);
          if (childEntity) {
            await this.destroyEntity(world, childEntity, true);
          }
        }
      }

      // 销毁实体
      await this.destroyEntity(world, entity, false);

      console.log(`实体已销毁: ${entity.name} (ID: ${entity.id})`);
      await this.triggerOutput(context, 'destroyed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('实体销毁失败:', error);
      throw error;
    }
  }

  private async destroyEntity(world: any, entity: any, isChild: boolean): Promise<void> {
    // 清理组件
    for (const [componentType, component] of entity.components) {
      if (component.destroy && typeof component.destroy === 'function') {
        await component.destroy();
      }
    }
    entity.components.clear();

    // 从父实体中移除
    if (entity.parent) {
      const parentEntity = world.entities.get(entity.parent);
      if (parentEntity) {
        parentEntity.children.delete(entity.id);
      }
    }

    // 从世界中移除
    world.entities.delete(entity.id);

    // 标记为已销毁
    entity.isActive = false;
    entity.destroyedAt = Date.now();
  }
}

/**
 * 123 - 组件添加节点
 * 向实体添加组件
 */
export class ComponentAddNode extends BaseNode {
  constructor() {
    super('ComponentAdd', '组件添加', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('componentType', '组件类型', DataType.STRING);
    this.addInputPort('componentData', '组件数据', DataType.OBJECT);
    this.addInputPort('componentClass', '组件类', DataType.FUNCTION);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('component', '组件', DataType.OBJECT);
    this.addOutputPort('added', '已添加', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const componentType = this.getInputValue('componentType');
    const componentData = this.getInputValue('componentData') || {};
    const componentClass = this.getInputValue('componentClass');

    if (!entity || !componentType) {
      throw new Error('需要实体和组件类型');
    }

    try {
      let component;

      if (componentClass && typeof componentClass === 'function') {
        // 使用组件类创建实例
        component = new componentClass(componentData);
      } else {
        // 创建默认组件对象
        component = {
          type: componentType,
          data: componentData,
          isActive: true,
          addedAt: Date.now()
        };
      }

      // 设置组件属性
      component.type = componentType;
      component.entityId = entity.id;

      // 初始化组件
      if (component.initialize && typeof component.initialize === 'function') {
        await component.initialize(entity);
      }

      // 添加到实体
      entity.components.set(componentType, component);

      this.setOutputValue('component', component);

      console.log(`组件已添加: ${componentType} -> 实体 ${entity.id}`);
      await this.triggerOutput(context, 'added');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('组件添加失败:', error);
      throw error;
    }
  }
}

/**
 * 124 - 组件移除节点
 * 从实体移除组件
 */
export class ComponentRemoveNode extends BaseNode {
  constructor() {
    super('ComponentRemove', '组件移除', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('componentType', '组件类型', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('component', '被移除的组件', DataType.OBJECT);
    this.addOutputPort('removed', '已移除', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const componentType = this.getInputValue('componentType');

    if (!entity || !componentType) {
      throw new Error('需要实体和组件类型');
    }

    try {
      const component = entity.components.get(componentType);
      
      if (!component) {
        console.warn(`组件不存在: ${componentType}`);
        await this.triggerOutput(context, 'output');
        return;
      }

      // 销毁组件
      if (component.destroy && typeof component.destroy === 'function') {
        await component.destroy();
      }

      // 从实体移除
      entity.components.delete(componentType);

      this.setOutputValue('component', component);

      console.log(`组件已移除: ${componentType} <- 实体 ${entity.id}`);
      await this.triggerOutput(context, 'removed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('组件移除失败:', error);
      throw error;
    }
  }
}

/**
 * 125 - 组件获取节点
 * 获取实体的组件
 */
export class ComponentGetNode extends BaseNode {
  constructor() {
    super('ComponentGet', '组件获取', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('componentType', '组件类型', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('component', '组件', DataType.OBJECT);
    this.addOutputPort('found', '已找到', DataType.TRIGGER);
    this.addOutputPort('notFound', '未找到', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const componentType = this.getInputValue('componentType');

    if (!entity || !componentType) {
      throw new Error('需要实体和组件类型');
    }

    try {
      const component = entity.components.get(componentType);

      if (component) {
        this.setOutputValue('component', component);
        await this.triggerOutput(context, 'found');
      } else {
        this.setOutputValue('component', null);
        await this.triggerOutput(context, 'notFound');
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('组件获取失败:', error);
      throw error;
    }
  }
}

/**
 * 126 - 实体查询节点
 * 根据组件类型查询实体
 */
export class EntityQueryNode extends BaseNode {
  constructor() {
    super('EntityQuery', '实体查询', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('world', '世界实例', DataType.OBJECT);
    this.addInputPort('componentTypes', '组件类型列表', DataType.ARRAY);
    this.addInputPort('queryType', '查询类型', DataType.STRING);
    this.addInputPort('filter', '过滤器', DataType.FUNCTION);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('entities', '实体列表', DataType.ARRAY);
    this.addOutputPort('count', '数量', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const world = this.getInputValue('world');
    const componentTypes = this.getInputValue('componentTypes') || [];
    const queryType = this.getInputValue('queryType') || 'all';
    const filter = this.getInputValue('filter');

    if (!world) {
      throw new Error('需要世界实例');
    }

    try {
      const results: any[] = [];

      for (const [entityId, entity] of world.entities) {
        if (!entity.isActive) continue;

        let matches = false;

        switch (queryType) {
          case 'all':
            matches = this.hasAllComponents(entity, componentTypes);
            break;
          case 'any':
            matches = this.hasAnyComponent(entity, componentTypes);
            break;
          case 'none':
            matches = !this.hasAnyComponent(entity, componentTypes);
            break;
          default:
            matches = true;
        }

        if (matches) {
          // 应用自定义过滤器
          if (filter && typeof filter === 'function') {
            try {
              matches = await filter(entity, context);
            } catch (error) {
              console.error('过滤器执行失败:', error);
              matches = false;
            }
          }

          if (matches) {
            results.push(entity);
          }
        }
      }

      this.setOutputValue('entities', results);
      this.setOutputValue('count', results.length);

      console.log(`实体查询完成，找到 ${results.length} 个实体`);
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('实体查询失败:', error);
      throw error;
    }
  }

  private hasAllComponents(entity: any, componentTypes: string[]): boolean {
    return componentTypes.every(type => entity.components.has(type));
  }

  private hasAnyComponent(entity: any, componentTypes: string[]): boolean {
    return componentTypes.some(type => entity.components.has(type));
  }
}

/**
 * 127 - 实体层级节点
 * 管理实体的父子关系
 */
export class EntityHierarchyNode extends BaseNode {
  constructor() {
    super('EntityHierarchy', '实体层级', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('parent', '父实体', DataType.OBJECT);
    this.addInputPort('operation', '操作', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('parent', '父实体', DataType.OBJECT);
    this.addOutputPort('children', '子实体列表', DataType.ARRAY);
    this.addOutputPort('attached', '已附加', DataType.TRIGGER);
    this.addOutputPort('detached', '已分离', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const parent = this.getInputValue('parent');
    const operation = this.getInputValue('operation') || 'getParent';

    if (!entity) {
      throw new Error('需要实体');
    }

    try {
      switch (operation) {
        case 'setParent':
          await this.setParent(entity, parent, context);
          break;
        case 'getParent':
          await this.getParent(entity, context);
          break;
        case 'getChildren':
          await this.getChildren(entity, context);
          break;
        case 'detach':
          await this.detachFromParent(entity, context);
          break;
        default:
          throw new Error(`未知操作: ${operation}`);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('实体层级操作失败:', error);
      throw error;
    }
  }

  private async setParent(entity: any, parent: any, context: IExecutionContext): Promise<void> {
    if (!parent) {
      throw new Error('需要父实体');
    }

    // 从当前父实体分离
    if (entity.parent) {
      await this.detachFromParent(entity, context);
    }

    // 设置新的父子关系
    entity.parent = parent.id;
    parent.children.add(entity.id);

    this.setOutputValue('parent', parent);

    console.log(`实体 ${entity.id} 已附加到父实体 ${parent.id}`);
    await this.triggerOutput(context, 'attached');
  }

  private async getParent(entity: any, context: IExecutionContext): Promise<void> {
    if (entity.parent) {
      // 这里需要从世界中获取父实体
      // 简化实现，假设可以通过某种方式获取
      const parentEntity = { id: entity.parent }; // 简化
      this.setOutputValue('parent', parentEntity);
    } else {
      this.setOutputValue('parent', null);
    }
  }

  private async getChildren(entity: any, context: IExecutionContext): Promise<void> {
    const children: any[] = [];

    for (const childId of entity.children) {
      // 这里需要从世界中获取子实体
      // 简化实现
      children.push({ id: childId });
    }

    this.setOutputValue('children', children);
  }

  private async detachFromParent(entity: any, context: IExecutionContext): Promise<void> {
    if (entity.parent) {
      // 这里需要从世界中获取父实体并移除关系
      // 简化实现
      entity.parent = null;

      console.log(`实体 ${entity.id} 已从父实体分离`);
      await this.triggerOutput(context, 'detached');
    }
  }
}
