/**
 * 系统管理节点 (101-120)
 * 实现引擎系统管理、世界创建、场景管理等核心功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 101 - 引擎初始化节点
 * 初始化引擎系统
 */
export class EngineInitializeNode extends BaseNode {
  private isInitialized = false;
  
  constructor() {
    super('EngineInitialize', '引擎初始化', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('config', '配置', DataType.OBJECT);
    this.addInputPort('plugins', '插件列表', DataType.ARRAY);
    this.addInputPort('debug', '调试模式', DataType.BOOLEAN);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('engine', '引擎实例', DataType.OBJECT);
    this.addOutputPort('initialized', '已初始化', DataType.TRIGGER);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    if (this.isInitialized) {
      console.warn('引擎已经初始化');
      await this.triggerOutput(context, 'output');
      return;
    }

    try {
      const config = this.getInputValue('config') || {};
      const plugins = this.getInputValue('plugins') || [];
      const debug = this.getInputValue('debug') || false;

      // 创建引擎实例
      const engine = {
        id: `engine_${Date.now()}`,
        config,
        plugins,
        debug,
        systems: new Map(),
        worlds: new Map(),
        scenes: new Map(),
        isRunning: false,
        startTime: Date.now(),
        frameCount: 0,
        deltaTime: 0,
        lastFrameTime: Date.now()
      };

      // 初始化核心系统
      await this.initializeCoreSystem(engine);
      
      // 加载插件
      for (const plugin of plugins) {
        await this.loadPlugin(engine, plugin);
      }

      this.isInitialized = true;
      this.setOutputValue('engine', engine);
      this.setOutputValue('error', null);

      console.log('引擎初始化完成');
      await this.triggerOutput(context, 'initialized');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('引擎初始化失败:', error);
      
      this.setOutputValue('engine', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }

  private async initializeCoreSystem(engine: any): Promise<void> {
    // 初始化时间系统
    engine.systems.set('time', {
      startTime: Date.now(),
      currentTime: Date.now(),
      deltaTime: 0,
      timeScale: 1.0,
      frameRate: 60
    });

    // 初始化事件系统
    engine.systems.set('event', {
      listeners: new Map(),
      eventQueue: []
    });

    // 初始化内存管理系统
    engine.systems.set('memory', {
      pools: new Map(),
      allocatedObjects: new Set(),
      gcThreshold: 1000
    });
  }

  private async loadPlugin(engine: any, plugin: any): Promise<void> {
    if (plugin && typeof plugin.initialize === 'function') {
      await plugin.initialize(engine);
      console.log(`插件已加载: ${plugin.name || 'unknown'}`);
    }
  }
}

/**
 * 102 - 世界创建节点
 * 创建游戏世界实例
 */
export class WorldCreateNode extends BaseNode {
  constructor() {
    super('WorldCreate', '世界创建', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('engine', '引擎实例', DataType.OBJECT);
    this.addInputPort('worldName', '世界名称', DataType.STRING);
    this.addInputPort('worldConfig', '世界配置', DataType.OBJECT);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('world', '世界实例', DataType.OBJECT);
    this.addOutputPort('created', '已创建', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const engine = this.getInputValue('engine');
    const worldName = this.getInputValue('worldName') || `world_${Date.now()}`;
    const worldConfig = this.getInputValue('worldConfig') || {};

    if (!engine) {
      throw new Error('需要引擎实例');
    }

    try {
      // 创建世界实例
      const world = {
        id: worldName,
        name: worldName,
        config: worldConfig,
        entities: new Map(),
        systems: new Map(),
        scenes: new Map(),
        activeScene: null,
        isActive: false,
        createdAt: Date.now(),
        entityIdCounter: 0
      };

      // 初始化世界系统
      await this.initializeWorldSystems(world);

      // 注册到引擎
      engine.worlds.set(worldName, world);

      this.setOutputValue('world', world);

      console.log(`世界已创建: ${worldName}`);
      await this.triggerOutput(context, 'created');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('世界创建失败:', error);
      throw error;
    }
  }

  private async initializeWorldSystems(world: any): Promise<void> {
    // 实体组件系统
    world.systems.set('ecs', {
      entities: world.entities,
      components: new Map(),
      systems: []
    });

    // 变换系统
    world.systems.set('transform', {
      transforms: new Map(),
      hierarchies: new Map()
    });

    // 场景系统
    world.systems.set('scene', {
      scenes: world.scenes,
      activeScene: null,
      loadingQueue: []
    });
  }
}

/**
 * 103 - 场景管理节点
 * 场景加载和切换管理
 */
export class SceneManagerNode extends BaseNode {
  constructor() {
    super('SceneManager', '场景管理', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('world', '世界实例', DataType.OBJECT);
    this.addInputPort('sceneName', '场景名称', DataType.STRING);
    this.addInputPort('sceneData', '场景数据', DataType.OBJECT);
    this.addInputPort('operation', '操作类型', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('scene', '场景实例', DataType.OBJECT);
    this.addOutputPort('loaded', '已加载', DataType.TRIGGER);
    this.addOutputPort('unloaded', '已卸载', DataType.TRIGGER);
    this.addOutputPort('switched', '已切换', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const world = this.getInputValue('world');
    const sceneName = this.getInputValue('sceneName');
    const sceneData = this.getInputValue('sceneData') || {};
    const operation = this.getInputValue('operation') || 'load';

    if (!world) {
      throw new Error('需要世界实例');
    }

    if (!sceneName) {
      throw new Error('需要场景名称');
    }

    try {
      switch (operation) {
        case 'load':
          await this.loadScene(world, sceneName, sceneData, context);
          break;
        case 'unload':
          await this.unloadScene(world, sceneName, context);
          break;
        case 'switch':
          await this.switchScene(world, sceneName, context);
          break;
        default:
          throw new Error(`未知操作类型: ${operation}`);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('场景管理操作失败:', error);
      throw error;
    }
  }

  private async loadScene(world: any, sceneName: string, sceneData: any, context: IExecutionContext): Promise<void> {
    if (world.scenes.has(sceneName)) {
      console.warn(`场景已存在: ${sceneName}`);
      return;
    }

    const scene = {
      id: sceneName,
      name: sceneName,
      data: sceneData,
      entities: new Set(),
      isLoaded: false,
      isActive: false,
      loadedAt: Date.now()
    };

    // 加载场景数据
    await this.loadSceneData(scene, sceneData);

    world.scenes.set(sceneName, scene);
    scene.isLoaded = true;

    this.setOutputValue('scene', scene);

    console.log(`场景已加载: ${sceneName}`);
    await this.triggerOutput(context, 'loaded');
  }

  private async unloadScene(world: any, sceneName: string, context: IExecutionContext): Promise<void> {
    const scene = world.scenes.get(sceneName);
    if (!scene) {
      console.warn(`场景不存在: ${sceneName}`);
      return;
    }

    // 清理场景实体
    for (const entityId of scene.entities) {
      world.entities.delete(entityId);
    }

    world.scenes.delete(sceneName);

    if (world.activeScene === sceneName) {
      world.activeScene = null;
    }

    console.log(`场景已卸载: ${sceneName}`);
    await this.triggerOutput(context, 'unloaded');
  }

  private async switchScene(world: any, sceneName: string, context: IExecutionContext): Promise<void> {
    const scene = world.scenes.get(sceneName);
    if (!scene) {
      throw new Error(`场景不存在: ${sceneName}`);
    }

    // 停用当前场景
    if (world.activeScene) {
      const currentScene = world.scenes.get(world.activeScene);
      if (currentScene) {
        currentScene.isActive = false;
      }
    }

    // 激活新场景
    world.activeScene = sceneName;
    scene.isActive = true;

    this.setOutputValue('scene', scene);

    console.log(`场景已切换: ${sceneName}`);
    await this.triggerOutput(context, 'switched');
  }

  private async loadSceneData(scene: any, sceneData: any): Promise<void> {
    // 加载场景中的实体
    if (sceneData.entities) {
      for (const entityData of sceneData.entities) {
        scene.entities.add(entityData.id);
      }
    }

    // 加载场景配置
    if (sceneData.config) {
      scene.config = sceneData.config;
    }
  }
}

/**
 * 104 - 系统注册节点
 * 注册和管理系统组件
 */
export class SystemRegisterNode extends BaseNode {
  constructor() {
    super('SystemRegister', '系统注册', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('engine', '引擎实例', DataType.OBJECT);
    this.addInputPort('systemName', '系统名称', DataType.STRING);
    this.addInputPort('systemClass', '系统类', DataType.FUNCTION);
    this.addInputPort('systemConfig', '系统配置', DataType.OBJECT);
    this.addInputPort('priority', '优先级', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('system', '系统实例', DataType.OBJECT);
    this.addOutputPort('registered', '已注册', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const engine = this.getInputValue('engine');
    const systemName = this.getInputValue('systemName');
    const systemClass = this.getInputValue('systemClass');
    const systemConfig = this.getInputValue('systemConfig') || {};
    const priority = this.getInputValue('priority') || 0;

    if (!engine) {
      throw new Error('需要引擎实例');
    }

    if (!systemName) {
      throw new Error('需要系统名称');
    }

    try {
      let system;

      if (systemClass && typeof systemClass === 'function') {
        // 创建系统实例
        system = new systemClass(systemConfig);
      } else {
        // 创建默认系统对象
        system = {
          name: systemName,
          config: systemConfig,
          priority,
          isActive: true,
          initialize: async () => {},
          update: async () => {},
          destroy: async () => {}
        };
      }

      // 设置系统属性
      system.name = systemName;
      system.priority = priority;
      system.registeredAt = Date.now();

      // 初始化系统
      if (typeof system.initialize === 'function') {
        await system.initialize(engine);
      }

      // 注册到引擎
      engine.systems.set(systemName, system);

      this.setOutputValue('system', system);

      console.log(`系统已注册: ${systemName}`);
      await this.triggerOutput(context, 'registered');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('系统注册失败:', error);
      throw error;
    }
  }
}

/**
 * 105 - 时间管理节点
 * 游戏时间和帧率控制
 */
export class TimeManagerNode extends BaseNode {
  private lastUpdateTime = 0;

  constructor() {
    super('TimeManager', '时间管理', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('engine', '引擎实例', DataType.OBJECT);
    this.addInputPort('timeScale', '时间缩放', DataType.NUMBER);
    this.addInputPort('targetFrameRate', '目标帧率', DataType.NUMBER);
    this.addInputPort('maxDeltaTime', '最大帧时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('deltaTime', '帧时间', DataType.NUMBER);
    this.addOutputPort('totalTime', '总时间', DataType.NUMBER);
    this.addOutputPort('frameRate', '当前帧率', DataType.NUMBER);
    this.addOutputPort('updated', '已更新', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const engine = this.getInputValue('engine');
    const timeScale = this.getInputValue('timeScale') || 1.0;
    const targetFrameRate = this.getInputValue('targetFrameRate') || 60;
    const maxDeltaTime = this.getInputValue('maxDeltaTime') || 0.1;

    if (!engine) {
      throw new Error('需要引擎实例');
    }

    try {
      const currentTime = Date.now();
      const timeSystem = engine.systems.get('time');

      if (!timeSystem) {
        throw new Error('时间系统未初始化');
      }

      // 计算帧时间
      let deltaTime = 0;
      if (this.lastUpdateTime > 0) {
        deltaTime = (currentTime - this.lastUpdateTime) / 1000.0;
        deltaTime = Math.min(deltaTime, maxDeltaTime);
        deltaTime *= timeScale;
      }

      this.lastUpdateTime = currentTime;

      // 更新时间系统
      timeSystem.currentTime = currentTime;
      timeSystem.deltaTime = deltaTime;
      timeSystem.timeScale = timeScale;
      timeSystem.targetFrameRate = targetFrameRate;

      // 计算总时间
      const totalTime = (currentTime - timeSystem.startTime) / 1000.0;

      // 计算当前帧率
      engine.frameCount++;
      const frameRate = deltaTime > 0 ? 1.0 / deltaTime : 0;

      // 更新引擎时间信息
      engine.deltaTime = deltaTime;
      engine.lastFrameTime = currentTime;

      this.setOutputValue('deltaTime', deltaTime);
      this.setOutputValue('totalTime', totalTime);
      this.setOutputValue('frameRate', frameRate);

      await this.triggerOutput(context, 'updated');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('时间管理更新失败:', error);
      throw error;
    }
  }
}

/**
 * 106 - 事件系统节点
 * 事件发布和订阅管理
 */
export class EventSystemNode extends BaseNode {
  constructor() {
    super('EventSystem', '事件系统', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('engine', '引擎实例', DataType.OBJECT);
    this.addInputPort('eventType', '事件类型', DataType.STRING);
    this.addInputPort('eventData', '事件数据', DataType.ANY);
    this.addInputPort('listener', '监听器', DataType.FUNCTION);
    this.addInputPort('operation', '操作', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('eventEmitted', '事件已发出', DataType.TRIGGER);
    this.addOutputPort('listenerAdded', '监听器已添加', DataType.TRIGGER);
    this.addOutputPort('listenerRemoved', '监听器已移除', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const engine = this.getInputValue('engine');
    const eventType = this.getInputValue('eventType');
    const eventData = this.getInputValue('eventData');
    const listener = this.getInputValue('listener');
    const operation = this.getInputValue('operation') || 'emit';

    if (!engine) {
      throw new Error('需要引擎实例');
    }

    const eventSystem = engine.systems.get('event');
    if (!eventSystem) {
      throw new Error('事件系统未初始化');
    }

    try {
      switch (operation) {
        case 'emit':
          await this.emitEvent(eventSystem, eventType, eventData, context);
          break;
        case 'addListener':
          await this.addListener(eventSystem, eventType, listener, context);
          break;
        case 'removeListener':
          await this.removeListener(eventSystem, eventType, listener, context);
          break;
        default:
          throw new Error(`未知操作: ${operation}`);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('事件系统操作失败:', error);
      throw error;
    }
  }

  private async emitEvent(eventSystem: any, eventType: string, eventData: any, context: IExecutionContext): Promise<void> {
    if (!eventType) {
      throw new Error('需要事件类型');
    }

    const event = {
      type: eventType,
      data: eventData,
      timestamp: Date.now()
    };

    // 添加到事件队列
    eventSystem.eventQueue.push(event);

    // 立即处理事件
    const listeners = eventSystem.listeners.get(eventType) || [];
    for (const listener of listeners) {
      try {
        if (typeof listener === 'function') {
          await listener(event, context);
        }
      } catch (error) {
        console.error(`事件监听器执行失败: ${eventType}`, error);
      }
    }

    console.log(`事件已发出: ${eventType}`);
    await this.triggerOutput(context, 'eventEmitted');
  }

  private async addListener(eventSystem: any, eventType: string, listener: Function, context: IExecutionContext): Promise<void> {
    if (!eventType || !listener) {
      throw new Error('需要事件类型和监听器');
    }

    if (!eventSystem.listeners.has(eventType)) {
      eventSystem.listeners.set(eventType, []);
    }

    const listeners = eventSystem.listeners.get(eventType);
    if (!listeners.includes(listener)) {
      listeners.push(listener);
    }

    console.log(`监听器已添加: ${eventType}`);
    await this.triggerOutput(context, 'listenerAdded');
  }

  private async removeListener(eventSystem: any, eventType: string, listener: Function, context: IExecutionContext): Promise<void> {
    if (!eventType || !listener) {
      throw new Error('需要事件类型和监听器');
    }

    const listeners = eventSystem.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }

    console.log(`监听器已移除: ${eventType}`);
    await this.triggerOutput(context, 'listenerRemoved');
  }
}

/**
 * 107 - 内存管理节点
 * 内存分配和垃圾回收管理
 */
export class MemoryManagerNode extends BaseNode {
  constructor() {
    super('MemoryManager', '内存管理', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('engine', '引擎实例', DataType.OBJECT);
    this.addInputPort('operation', '操作', DataType.STRING);
    this.addInputPort('objectType', '对象类型', DataType.STRING);
    this.addInputPort('poolSize', '池大小', DataType.NUMBER);
    this.addInputPort('gcThreshold', 'GC阈值', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('object', '对象', DataType.OBJECT);
    this.addOutputPort('memoryInfo', '内存信息', DataType.OBJECT);
    this.addOutputPort('poolCreated', '池已创建', DataType.TRIGGER);
    this.addOutputPort('gcTriggered', 'GC已触发', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const engine = this.getInputValue('engine');
    const operation = this.getInputValue('operation') || 'getInfo';
    const objectType = this.getInputValue('objectType');
    const poolSize = this.getInputValue('poolSize') || 100;
    const gcThreshold = this.getInputValue('gcThreshold') || 1000;

    if (!engine) {
      throw new Error('需要引擎实例');
    }

    const memorySystem = engine.systems.get('memory');
    if (!memorySystem) {
      throw new Error('内存系统未初始化');
    }

    try {
      switch (operation) {
        case 'createPool':
          await this.createObjectPool(memorySystem, objectType, poolSize, context);
          break;
        case 'allocate':
          await this.allocateObject(memorySystem, objectType, context);
          break;
        case 'deallocate':
          await this.deallocateObject(memorySystem, objectType, context);
          break;
        case 'gc':
          await this.triggerGarbageCollection(memorySystem, context);
          break;
        case 'getInfo':
          await this.getMemoryInfo(memorySystem, context);
          break;
        default:
          throw new Error(`未知操作: ${operation}`);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('内存管理操作失败:', error);
      throw error;
    }
  }

  private async createObjectPool(memorySystem: any, objectType: string, poolSize: number, context: IExecutionContext): Promise<void> {
    if (!objectType) {
      throw new Error('需要对象类型');
    }

    const pool = {
      type: objectType,
      size: poolSize,
      available: [],
      allocated: new Set(),
      createdAt: Date.now()
    };

    // 预分配对象
    for (let i = 0; i < poolSize; i++) {
      pool.available.push(this.createObject(objectType));
    }

    memorySystem.pools.set(objectType, pool);

    console.log(`对象池已创建: ${objectType}, 大小: ${poolSize}`);
    await this.triggerOutput(context, 'poolCreated');
  }

  private async allocateObject(memorySystem: any, objectType: string, context: IExecutionContext): Promise<void> {
    const pool = memorySystem.pools.get(objectType);
    let obj;

    if (pool && pool.available.length > 0) {
      // 从池中获取对象
      obj = pool.available.pop();
      pool.allocated.add(obj);
    } else {
      // 创建新对象
      obj = this.createObject(objectType);
      memorySystem.allocatedObjects.add(obj);
    }

    this.setOutputValue('object', obj);
  }

  private async deallocateObject(memorySystem: any, objectType: string, context: IExecutionContext): Promise<void> {
    const obj = this.getInputValue('object');
    if (!obj) return;

    const pool = memorySystem.pools.get(objectType);
    if (pool && pool.allocated.has(obj)) {
      // 返回到池中
      pool.allocated.delete(obj);
      this.resetObject(obj);
      pool.available.push(obj);
    } else {
      // 从分配列表中移除
      memorySystem.allocatedObjects.delete(obj);
    }
  }

  private async triggerGarbageCollection(memorySystem: any, context: IExecutionContext): Promise<void> {
    let collected = 0;

    // 清理未使用的对象
    for (const obj of memorySystem.allocatedObjects) {
      if (this.isObjectUnused(obj)) {
        memorySystem.allocatedObjects.delete(obj);
        collected++;
      }
    }

    console.log(`垃圾回收完成，清理对象: ${collected}`);
    await this.triggerOutput(context, 'gcTriggered');
  }

  private async getMemoryInfo(memorySystem: any, context: IExecutionContext): Promise<void> {
    const info = {
      pools: memorySystem.pools.size,
      allocatedObjects: memorySystem.allocatedObjects.size,
      totalPoolObjects: 0,
      availablePoolObjects: 0
    };

    for (const pool of memorySystem.pools.values()) {
      info.totalPoolObjects += pool.size;
      info.availablePoolObjects += pool.available.length;
    }

    this.setOutputValue('memoryInfo', info);
  }

  private createObject(type: string): any {
    // 根据类型创建对象
    switch (type) {
      case 'vector3':
        return { x: 0, y: 0, z: 0 };
      case 'matrix4':
        return new Array(16).fill(0);
      case 'entity':
        return { id: 0, components: new Map() };
      default:
        return {};
    }
  }

  private resetObject(obj: any): void {
    // 重置对象状态
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (typeof obj[key] === 'number') {
          obj[key] = 0;
        } else if (typeof obj[key] === 'string') {
          obj[key] = '';
        } else if (Array.isArray(obj[key])) {
          obj[key].length = 0;
        }
      });
    }
  }

  private isObjectUnused(obj: any): boolean {
    // 简化的未使用检测
    return obj && obj.lastUsed && (Date.now() - obj.lastUsed) > 60000;
  }
}
