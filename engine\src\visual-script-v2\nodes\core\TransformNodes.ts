/**
 * 基础变换节点 (141-150)
 * 实现位置、旋转、缩放、变换矩阵等基础变换功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 141 - 位置设置节点
 * 设置实体的位置
 */
export class PositionSetNode extends BaseNode {
  constructor() {
    super('PositionSet', '位置设置', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('x', 'X坐标', DataType.NUMBER);
    this.addInputPort('y', 'Y坐标', DataType.NUMBER);
    this.addInputPort('z', 'Z坐标', DataType.NUMBER);
    this.addInputPort('position', '位置向量', DataType.OBJECT);
    this.addInputPort('space', '坐标空间', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('newPosition', '新位置', DataType.OBJECT);
    this.addOutputPort('updated', '已更新', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const x = this.getInputValue('x') || 0;
    const y = this.getInputValue('y') || 0;
    const z = this.getInputValue('z') || 0;
    const position = this.getInputValue('position');
    const space = this.getInputValue('space') || 'world';

    if (!entity) {
      throw new Error('需要实体');
    }

    try {
      // 获取或创建变换组件
      let transform = entity.components.get('transform');
      if (!transform) {
        transform = this.createTransformComponent();
        entity.components.set('transform', transform);
      }

      // 设置位置
      let newPosition;
      if (position && typeof position === 'object') {
        newPosition = {
          x: position.x || 0,
          y: position.y || 0,
          z: position.z || 0
        };
      } else {
        newPosition = { x, y, z };
      }

      if (space === 'local') {
        transform.data.localPosition = newPosition;
        // 计算世界位置
        transform.data.worldPosition = this.calculateWorldPosition(entity, newPosition);
      } else {
        transform.data.worldPosition = newPosition;
        // 计算本地位置
        transform.data.localPosition = this.calculateLocalPosition(entity, newPosition);
      }

      this.setOutputValue('newPosition', newPosition);

      console.log(`实体 ${entity.id} 位置已设置: (${newPosition.x}, ${newPosition.y}, ${newPosition.z})`);
      await this.triggerOutput(context, 'updated');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('位置设置失败:', error);
      throw error;
    }
  }

  private createTransformComponent(): any {
    return {
      type: 'transform',
      data: {
        localPosition: { x: 0, y: 0, z: 0 },
        worldPosition: { x: 0, y: 0, z: 0 },
        localRotation: { x: 0, y: 0, z: 0, w: 1 },
        worldRotation: { x: 0, y: 0, z: 0, w: 1 },
        localScale: { x: 1, y: 1, z: 1 },
        worldScale: { x: 1, y: 1, z: 1 },
        matrix: this.createIdentityMatrix()
      },
      isActive: true,
      addedAt: Date.now()
    };
  }

  private calculateWorldPosition(entity: any, localPosition: any): any {
    // 简化实现，实际需要考虑父实体的变换
    if (entity.parent) {
      // 这里应该获取父实体的变换并应用
      return localPosition; // 简化
    }
    return localPosition;
  }

  private calculateLocalPosition(entity: any, worldPosition: any): any {
    // 简化实现，实际需要考虑父实体的变换
    if (entity.parent) {
      // 这里应该获取父实体的变换并反向应用
      return worldPosition; // 简化
    }
    return worldPosition;
  }

  private createIdentityMatrix(): number[] {
    return [
      1, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    ];
  }
}

/**
 * 142 - 旋转设置节点
 * 设置实体的旋转
 */
export class RotationSetNode extends BaseNode {
  constructor() {
    super('RotationSet', '旋转设置', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('x', 'X轴旋转', DataType.NUMBER);
    this.addInputPort('y', 'Y轴旋转', DataType.NUMBER);
    this.addInputPort('z', 'Z轴旋转', DataType.NUMBER);
    this.addInputPort('quaternion', '四元数', DataType.OBJECT);
    this.addInputPort('angleMode', '角度模式', DataType.STRING);
    this.addInputPort('space', '坐标空间', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('newRotation', '新旋转', DataType.OBJECT);
    this.addOutputPort('updated', '已更新', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const x = this.getInputValue('x') || 0;
    const y = this.getInputValue('y') || 0;
    const z = this.getInputValue('z') || 0;
    const quaternion = this.getInputValue('quaternion');
    const angleMode = this.getInputValue('angleMode') || 'degrees';
    const space = this.getInputValue('space') || 'world';

    if (!entity) {
      throw new Error('需要实体');
    }

    try {
      // 获取或创建变换组件
      let transform = entity.components.get('transform');
      if (!transform) {
        transform = this.createTransformComponent();
        entity.components.set('transform', transform);
      }

      // 设置旋转
      let newRotation;
      if (quaternion && typeof quaternion === 'object') {
        newRotation = {
          x: quaternion.x || 0,
          y: quaternion.y || 0,
          z: quaternion.z || 0,
          w: quaternion.w || 1
        };
      } else {
        // 将欧拉角转换为四元数
        const radians = angleMode === 'degrees' ? {
          x: x * Math.PI / 180,
          y: y * Math.PI / 180,
          z: z * Math.PI / 180
        } : { x, y, z };
        
        newRotation = this.eulerToQuaternion(radians.x, radians.y, radians.z);
      }

      if (space === 'local') {
        transform.data.localRotation = newRotation;
        transform.data.worldRotation = this.calculateWorldRotation(entity, newRotation);
      } else {
        transform.data.worldRotation = newRotation;
        transform.data.localRotation = this.calculateLocalRotation(entity, newRotation);
      }

      this.setOutputValue('newRotation', newRotation);

      console.log(`实体 ${entity.id} 旋转已设置`);
      await this.triggerOutput(context, 'updated');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('旋转设置失败:', error);
      throw error;
    }
  }

  private createTransformComponent(): any {
    return {
      type: 'transform',
      data: {
        localPosition: { x: 0, y: 0, z: 0 },
        worldPosition: { x: 0, y: 0, z: 0 },
        localRotation: { x: 0, y: 0, z: 0, w: 1 },
        worldRotation: { x: 0, y: 0, z: 0, w: 1 },
        localScale: { x: 1, y: 1, z: 1 },
        worldScale: { x: 1, y: 1, z: 1 },
        matrix: this.createIdentityMatrix()
      },
      isActive: true,
      addedAt: Date.now()
    };
  }

  private eulerToQuaternion(x: number, y: number, z: number): any {
    const cx = Math.cos(x * 0.5);
    const sx = Math.sin(x * 0.5);
    const cy = Math.cos(y * 0.5);
    const sy = Math.sin(y * 0.5);
    const cz = Math.cos(z * 0.5);
    const sz = Math.sin(z * 0.5);

    return {
      x: sx * cy * cz - cx * sy * sz,
      y: cx * sy * cz + sx * cy * sz,
      z: cx * cy * sz - sx * sy * cz,
      w: cx * cy * cz + sx * sy * sz
    };
  }

  private calculateWorldRotation(entity: any, localRotation: any): any {
    // 简化实现
    return localRotation;
  }

  private calculateLocalRotation(entity: any, worldRotation: any): any {
    // 简化实现
    return worldRotation;
  }

  private createIdentityMatrix(): number[] {
    return [
      1, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    ];
  }
}

/**
 * 143 - 缩放设置节点
 * 设置实体的缩放
 */
export class ScaleSetNode extends BaseNode {
  constructor() {
    super('ScaleSet', '缩放设置', NodeCategory.CORE);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('x', 'X轴缩放', DataType.NUMBER);
    this.addInputPort('y', 'Y轴缩放', DataType.NUMBER);
    this.addInputPort('z', 'Z轴缩放', DataType.NUMBER);
    this.addInputPort('scale', '缩放向量', DataType.OBJECT);
    this.addInputPort('uniform', '统一缩放', DataType.NUMBER);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('newScale', '新缩放', DataType.OBJECT);
    this.addOutputPort('updated', '已更新', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const x = this.getInputValue('x');
    const y = this.getInputValue('y');
    const z = this.getInputValue('z');
    const scale = this.getInputValue('scale');
    const uniform = this.getInputValue('uniform');

    if (!entity) {
      throw new Error('需要实体');
    }

    try {
      // 获取或创建变换组件
      let transform = entity.components.get('transform');
      if (!transform) {
        transform = this.createTransformComponent();
        entity.components.set('transform', transform);
      }

      // 设置缩放
      let newScale;
      if (uniform !== undefined) {
        newScale = { x: uniform, y: uniform, z: uniform };
      } else if (scale && typeof scale === 'object') {
        newScale = {
          x: scale.x !== undefined ? scale.x : 1,
          y: scale.y !== undefined ? scale.y : 1,
          z: scale.z !== undefined ? scale.z : 1
        };
      } else {
        newScale = {
          x: x !== undefined ? x : 1,
          y: y !== undefined ? y : 1,
          z: z !== undefined ? z : 1
        };
      }

      transform.data.localScale = newScale;
      transform.data.worldScale = this.calculateWorldScale(entity, newScale);

      this.setOutputValue('newScale', newScale);

      console.log(`实体 ${entity.id} 缩放已设置: (${newScale.x}, ${newScale.y}, ${newScale.z})`);
      await this.triggerOutput(context, 'updated');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('缩放设置失败:', error);
      throw error;
    }
  }

  private createTransformComponent(): any {
    return {
      type: 'transform',
      data: {
        localPosition: { x: 0, y: 0, z: 0 },
        worldPosition: { x: 0, y: 0, z: 0 },
        localRotation: { x: 0, y: 0, z: 0, w: 1 },
        worldRotation: { x: 0, y: 0, z: 0, w: 1 },
        localScale: { x: 1, y: 1, z: 1 },
        worldScale: { x: 1, y: 1, z: 1 },
        matrix: this.createIdentityMatrix()
      },
      isActive: true,
      addedAt: Date.now()
    };
  }

  private calculateWorldScale(entity: any, localScale: any): any {
    // 简化实现，实际需要考虑父实体的缩放
    if (entity.parent) {
      // 这里应该获取父实体的缩放并相乘
      return localScale; // 简化
    }
    return localScale;
  }

  private createIdentityMatrix(): number[] {
    return [
      1, 0, 0, 0,
      0, 1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    ];
  }
}

/**
 * 144 - 变换矩阵节点
 * 计算和应用变换矩阵
 */
export class TransformMatrixNode extends BaseNode {
  constructor() {
    super('TransformMatrix', '变换矩阵', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('matrix', '矩阵', DataType.ARRAY);
    this.addInputPort('operation', '操作', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('matrix', '变换矩阵', DataType.ARRAY);
    this.addOutputPort('position', '位置', DataType.OBJECT);
    this.addOutputPort('rotation', '旋转', DataType.OBJECT);
    this.addOutputPort('scale', '缩放', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const matrix = this.getInputValue('matrix');
    const operation = this.getInputValue('operation') || 'calculate';

    if (!entity) {
      throw new Error('需要实体');
    }

    try {
      const transform = entity.components.get('transform');
      if (!transform) {
        throw new Error('实体没有变换组件');
      }

      switch (operation) {
        case 'calculate':
          await this.calculateMatrix(transform, context);
          break;
        case 'apply':
          await this.applyMatrix(transform, matrix, context);
          break;
        case 'decompose':
          await this.decomposeMatrix(transform, context);
          break;
        default:
          throw new Error(`未知操作: ${operation}`);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('变换矩阵操作失败:', error);
      throw error;
    }
  }

  private async calculateMatrix(transform: any, context: IExecutionContext): Promise<void> {
    const pos = transform.data.localPosition;
    const rot = transform.data.localRotation;
    const scale = transform.data.localScale;

    // 计算变换矩阵 (简化实现)
    const matrix = this.createTransformMatrix(pos, rot, scale);
    transform.data.matrix = matrix;

    this.setOutputValue('matrix', matrix);
    this.setOutputValue('position', pos);
    this.setOutputValue('rotation', rot);
    this.setOutputValue('scale', scale);
  }

  private async applyMatrix(transform: any, matrix: number[], context: IExecutionContext): Promise<void> {
    if (!matrix || matrix.length !== 16) {
      throw new Error('需要4x4矩阵');
    }

    transform.data.matrix = [...matrix];

    // 从矩阵分解出位置、旋转、缩放
    const decomposed = this.decomposeTransformMatrix(matrix);
    transform.data.localPosition = decomposed.position;
    transform.data.localRotation = decomposed.rotation;
    transform.data.localScale = decomposed.scale;

    this.setOutputValue('matrix', matrix);
    this.setOutputValue('position', decomposed.position);
    this.setOutputValue('rotation', decomposed.rotation);
    this.setOutputValue('scale', decomposed.scale);
  }

  private async decomposeMatrix(transform: any, context: IExecutionContext): Promise<void> {
    const matrix = transform.data.matrix;
    const decomposed = this.decomposeTransformMatrix(matrix);

    this.setOutputValue('matrix', matrix);
    this.setOutputValue('position', decomposed.position);
    this.setOutputValue('rotation', decomposed.rotation);
    this.setOutputValue('scale', decomposed.scale);
  }

  private createTransformMatrix(position: any, rotation: any, scale: any): number[] {
    // 简化的变换矩阵计算
    const matrix = [
      scale.x, 0, 0, position.x,
      0, scale.y, 0, position.y,
      0, 0, scale.z, position.z,
      0, 0, 0, 1
    ];

    // 这里应该包含旋转计算，简化处理
    return matrix;
  }

  private decomposeTransformMatrix(matrix: number[]): any {
    // 简化的矩阵分解
    return {
      position: { x: matrix[3], y: matrix[7], z: matrix[11] },
      rotation: { x: 0, y: 0, z: 0, w: 1 },
      scale: { x: matrix[0], y: matrix[5], z: matrix[10] }
    };
  }
}

/**
 * 145 - 坐标转换节点
 * 世界坐标和本地坐标转换
 */
export class CoordinateConvertNode extends BaseNode {
  constructor() {
    super('CoordinateConvert', '坐标转换', NodeCategory.CORE);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('entity', '实体', DataType.OBJECT);
    this.addInputPort('point', '坐标点', DataType.OBJECT);
    this.addInputPort('fromSpace', '源坐标空间', DataType.STRING);
    this.addInputPort('toSpace', '目标坐标空间', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('convertedPoint', '转换后坐标', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const point = this.getInputValue('point');
    const fromSpace = this.getInputValue('fromSpace') || 'local';
    const toSpace = this.getInputValue('toSpace') || 'world';

    if (!entity || !point) {
      throw new Error('需要实体和坐标点');
    }

    try {
      const transform = entity.components.get('transform');
      if (!transform) {
        throw new Error('实体没有变换组件');
      }

      let convertedPoint;

      if (fromSpace === 'local' && toSpace === 'world') {
        convertedPoint = this.localToWorld(point, transform);
      } else if (fromSpace === 'world' && toSpace === 'local') {
        convertedPoint = this.worldToLocal(point, transform);
      } else if (fromSpace === toSpace) {
        convertedPoint = { ...point };
      } else {
        throw new Error(`不支持的坐标空间转换: ${fromSpace} -> ${toSpace}`);
      }

      this.setOutputValue('convertedPoint', convertedPoint);

      console.log(`坐标转换完成: ${fromSpace} -> ${toSpace}`);
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('坐标转换失败:', error);
      throw error;
    }
  }

  private localToWorld(localPoint: any, transform: any): any {
    // 简化实现，实际需要应用完整的变换矩阵
    const worldPos = transform.data.worldPosition;
    const scale = transform.data.worldScale;

    return {
      x: localPoint.x * scale.x + worldPos.x,
      y: localPoint.y * scale.y + worldPos.y,
      z: localPoint.z * scale.z + worldPos.z
    };
  }

  private worldToLocal(worldPoint: any, transform: any): any {
    // 简化实现，实际需要应用逆变换矩阵
    const worldPos = transform.data.worldPosition;
    const scale = transform.data.worldScale;

    return {
      x: (worldPoint.x - worldPos.x) / scale.x,
      y: (worldPoint.y - worldPos.y) / scale.y,
      z: (worldPoint.z - worldPos.z) / scale.z
    };
  }
}
