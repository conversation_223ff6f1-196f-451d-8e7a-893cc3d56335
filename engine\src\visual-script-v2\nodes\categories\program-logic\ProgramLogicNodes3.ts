/**
 * 第一批次：程序逻辑控制基础节点 (021-050)
 * 完成剩余的高级流程控制和函数式编程节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 021 - 函数调用 (Function Call)
 * 调用自定义函数
 */
export class FunctionCallNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/function-call', '函数调用', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发函数调用'
      },
      {
        name: 'functionName',
        label: '函数名',
        type: DataType.STRING,
        direction: 'input',
        required: true,
        description: '要调用的函数名'
      },
      {
        name: 'param1',
        label: '参数1',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '第一个参数'
      },
      {
        name: 'param2',
        label: '参数2',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '第二个参数'
      },
      {
        name: 'param3',
        label: '参数3',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '第三个参数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '函数调用完成'
      },
      {
        name: 'result',
        label: '返回值',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '函数返回值'
      },
      {
        name: 'error',
        label: '错误',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '调用错误信息'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      const functionName = context.getInputValue('functionName');
      const param1 = context.getInputValue('param1');
      const param2 = context.getInputValue('param2');
      const param3 = context.getInputValue('param3');

      // 在实际实现中，这里应该从函数注册表中查找并调用函数
      context.log('info', `调用函数: ${functionName}`, param1, param2, param3);
      
      // 模拟函数调用结果
      const result = `函数 ${functionName} 的返回值`;
      context.setOutputValue('result', result);
      context.setOutputValue('completed', true);
    } catch (error) {
      context.setOutputValue('error', error instanceof Error ? error.message : String(error));
    }
  }
}

/**
 * 022 - 函数定义 (Function Definition)
 * 定义自定义函数
 */
export class FunctionDefinitionNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/function-definition', '函数定义', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'functionName',
        label: '函数名',
        type: DataType.STRING,
        direction: 'input',
        required: true,
        description: '函数名称'
      },
      {
        name: 'param1Name',
        label: '参数1名称',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '第一个参数名称'
      },
      {
        name: 'param2Name',
        label: '参数2名称',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '第二个参数名称'
      },
      {
        name: 'param3Name',
        label: '参数3名称',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '第三个参数名称'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'functionBody',
        label: '函数体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '函数体执行入口'
      },
      {
        name: 'param1',
        label: '参数1',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '第一个参数值'
      },
      {
        name: 'param2',
        label: '参数2',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '第二个参数值'
      },
      {
        name: 'param3',
        label: '参数3',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '第三个参数值'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const functionName = context.getInputValue('functionName');
    context.log('info', `定义函数: ${functionName}`);
    
    // 在实际实现中，这里应该注册函数到函数注册表
    context.setOutputValue('functionBody', true);
  }
}

/**
 * 023 - 返回值 (Return)
 * 函数返回值
 */
export class ReturnNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/return', '返回值', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发返回'
      },
      {
        name: 'value',
        label: '返回值',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要返回的值'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'returned',
        label: '已返回',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '返回完成'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const value = context.getInputValue('value');
    context.log('info', '函数返回:', value);
    
    // 在实际实现中，这里应该设置函数的返回值
    context.setOutputValue('returned', true);
  }
}

/**
 * 024 - 递归调用 (Recursive Call)
 * 递归函数调用
 */
export class RecursiveCallNode extends BaseNode {
  private callDepth = 0;

  constructor(id?: string) {
    super(id, 'program-logic/recursive-call', '递归调用', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发递归调用'
      },
      {
        name: 'condition',
        label: '递归条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        description: '是否继续递归'
      },
      {
        name: 'maxDepth',
        label: '最大深度',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 100,
        description: '最大递归深度'
      },
      {
        name: 'parameter',
        label: '递归参数',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '递归传递的参数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'recurse',
        label: '递归',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '继续递归调用'
      },
      {
        name: 'baseCase',
        label: '基础情况',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '递归终止条件'
      },
      {
        name: 'depth',
        label: '当前深度',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前递归深度'
      },
      {
        name: 'parameter',
        label: '参数',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '当前参数值'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const condition = context.getInputValue('condition');
    const maxDepth = context.getInputValue('maxDepth') || 100;
    const parameter = context.getInputValue('parameter');

    this.callDepth++;
    context.setOutputValue('depth', this.callDepth);
    context.setOutputValue('parameter', parameter);

    if (condition && this.callDepth < maxDepth) {
      context.setOutputValue('recurse', true);
    } else {
      context.setOutputValue('baseCase', true);
      this.callDepth = 0; // 重置深度
    }
  }
}

/**
 * 025 - 高阶函数 (Higher Order Function)
 * 接受函数作为参数的函数
 */
export class HigherOrderFunctionNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/higher-order-function', '高阶函数', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发执行'
      },
      {
        name: 'function',
        label: '函数',
        type: DataType.FUNCTION,
        direction: 'input',
        required: true,
        description: '要执行的函数'
      },
      {
        name: 'data',
        label: '数据',
        type: DataType.ARRAY,
        direction: 'input',
        required: false,
        description: '要处理的数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '处理结果'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '处理完成'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = context.getInputValue('function');
    const data = context.getInputValue('data') || [];

    // 在实际实现中，这里应该执行传入的函数
    context.log('info', '执行高阶函数', func, data);
    
    // 模拟函数执行结果
    const result = data.map((item: any) => `processed_${item}`);
    context.setOutputValue('result', result);
    context.setOutputValue('completed', true);
  }
}

/**
 * 026 - 闭包 (Closure)
 * 闭包函数实现
 */
export class ClosureNode extends BaseNode {
  private capturedVariables = new Map<string, any>();

  constructor(id?: string) {
    super(id, 'program-logic/closure', '闭包', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'capture',
        label: '捕获',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '捕获外部变量'
      },
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '执行闭包函数'
      },
      {
        name: 'variableName',
        label: '变量名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要捕获的变量名'
      },
      {
        name: 'variableValue',
        label: '变量值',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要捕获的变量值'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'closureBody',
        label: '闭包体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '闭包函数体'
      },
      {
        name: 'capturedValue',
        label: '捕获的值',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '捕获的变量值'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const captureTrigger = context.getInputValue('capture');
    const executeTrigger = context.getInputValue('execute');
    const variableName = context.getInputValue('variableName');
    const variableValue = context.getInputValue('variableValue');

    if (captureTrigger && variableName) {
      this.capturedVariables.set(variableName, variableValue);
      context.log('info', `捕获变量: ${variableName} = ${variableValue}`);
    }

    if (executeTrigger) {
      context.setOutputValue('closureBody', true);
      // 输出所有捕获的变量
      for (const [name, value] of this.capturedVariables) {
        context.setOutputValue('capturedValue', value);
        break; // 简化实现，只输出第一个
      }
    }
  }
}

/**
 * 027 - 柯里化 (Currying)
 * 函数柯里化
 */
export class CurryingNode extends BaseNode {
  private partialArgs: any[] = [];

  constructor(id?: string) {
    super(id, 'program-logic/currying', '柯里化', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'addArg',
        label: '添加参数',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加部分参数'
      },
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '执行柯里化函数'
      },
      {
        name: 'argument',
        label: '参数',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要添加的参数'
      },
      {
        name: 'expectedArgs',
        label: '期望参数数量',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 3,
        description: '期望的参数总数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'partial',
        label: '部分应用',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '参数不足，返回部分应用函数'
      },
      {
        name: 'complete',
        label: '完整调用',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '参数足够，执行完整函数'
      },
      {
        name: 'arguments',
        label: '参数列表',
        type: DataType.ARRAY,
        direction: 'output',
        required: false,
        description: '当前收集的参数'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const addArgTrigger = context.getInputValue('addArg');
    const executeTrigger = context.getInputValue('execute');
    const argument = context.getInputValue('argument');
    const expectedArgs = context.getInputValue('expectedArgs') || 3;

    if (addArgTrigger && argument !== undefined) {
      this.partialArgs.push(argument);
    }

    context.setOutputValue('arguments', [...this.partialArgs]);

    if (executeTrigger) {
      if (this.partialArgs.length >= expectedArgs) {
        context.setOutputValue('complete', true);
        this.partialArgs = []; // 重置参数
      } else {
        context.setOutputValue('partial', true);
      }
    }
  }
}

/**
 * 028 - 管道操作 (Pipeline)
 * 函数管道操作
 */
export class PipelineNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/pipeline', '管道操作', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始管道处理'
      },
      {
        name: 'input',
        label: '输入',
        type: DataType.ANY,
        direction: 'input',
        required: true,
        description: '管道输入数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'step1',
        label: '步骤1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '管道第一步'
      },
      {
        name: 'step2',
        label: '步骤2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '管道第二步'
      },
      {
        name: 'step3',
        label: '步骤3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '管道第三步'
      },
      {
        name: 'output',
        label: '输出',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '管道最终输出'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const input = context.getInputValue('input');

    // 模拟管道处理
    context.setOutputValue('step1', true);
    await new Promise(resolve => setTimeout(resolve, 0));

    context.setOutputValue('step2', true);
    await new Promise(resolve => setTimeout(resolve, 0));

    context.setOutputValue('step3', true);
    await new Promise(resolve => setTimeout(resolve, 0));

    // 输出处理结果
    context.setOutputValue('output', `processed_${input}`);
  }
}

/**
 * 029 - 组合函数 (Function Composition)
 * 函数组合
 */
export class FunctionCompositionNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/function-composition', '组合函数', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '执行组合函数'
      },
      {
        name: 'input',
        label: '输入',
        type: DataType.ANY,
        direction: 'input',
        required: true,
        description: '组合函数输入'
      },
      {
        name: 'function1',
        label: '函数1',
        type: DataType.FUNCTION,
        direction: 'input',
        required: false,
        description: '第一个函数'
      },
      {
        name: 'function2',
        label: '函数2',
        type: DataType.FUNCTION,
        direction: 'input',
        required: false,
        description: '第二个函数'
      },
      {
        name: 'function3',
        label: '函数3',
        type: DataType.FUNCTION,
        direction: 'input',
        required: false,
        description: '第三个函数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '组合函数结果'
      },
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '组合执行完成'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const input = context.getInputValue('input');
    const func1 = context.getInputValue('function1');
    const func2 = context.getInputValue('function2');
    const func3 = context.getInputValue('function3');

    // 模拟函数组合 f3(f2(f1(input)))
    let result = input;

    if (func1) {
      result = `f1(${result})`;
    }
    if (func2) {
      result = `f2(${result})`;
    }
    if (func3) {
      result = `f3(${result})`;
    }

    context.setOutputValue('result', result);
    context.setOutputValue('completed', true);
  }
}

/**
 * 030 - 惰性求值 (Lazy Evaluation)
 * 惰性求值实现
 */
export class LazyEvaluationNode extends BaseNode {
  private isEvaluated = false;
  private cachedResult: any = null;

  constructor(id?: string) {
    super(id, 'program-logic/lazy-evaluation', '惰性求值', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'getValue',
        label: '获取值',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '触发求值'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置缓存'
      },
      {
        name: 'expression',
        label: '表达式',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '要求值的表达式'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'evaluate',
        label: '求值',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '触发表达式求值'
      },
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '求值结果'
      },
      {
        name: 'fromCache',
        label: '来自缓存',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '结果是否来自缓存'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const getValueTrigger = context.getInputValue('getValue');
    const resetTrigger = context.getInputValue('reset');
    const expression = context.getInputValue('expression');

    if (resetTrigger) {
      this.isEvaluated = false;
      this.cachedResult = null;
    }

    if (getValueTrigger) {
      if (!this.isEvaluated) {
        // 第一次求值
        context.setOutputValue('evaluate', true);
        this.cachedResult = `evaluated_${expression}`;
        this.isEvaluated = true;
        context.setOutputValue('fromCache', false);
      } else {
        // 使用缓存结果
        context.setOutputValue('fromCache', true);
      }

      context.setOutputValue('result', this.cachedResult);
    }
  }
}
