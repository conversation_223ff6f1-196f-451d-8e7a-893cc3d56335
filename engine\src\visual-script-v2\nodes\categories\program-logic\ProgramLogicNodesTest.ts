/**
 * 第一批次：程序逻辑控制基础节点测试 (001-050)
 * 验证程序逻辑控制节点的功能和集成
 */

import { 
  StartNode, 
  EndNode, 
  SequenceNode, 
  BranchNode, 
  SwitchNode,
  LoopControlNode,
  ForLoopNode,
  WhileLoopNode,
  DelayNode,
  GateNode
} from './ProgramLogicNodes';

import {
  FlipFlopNode,
  ParallelNode,
  RaceNode,
  SynchronizationNode,
  WaitUntilNode
} from './ProgramLogicNodes2';

import {
  FunctionCallNode,
  FunctionDefinitionNode,
  ReturnNode,
  RecursiveCallNode,
  HigherOrderFunctionNode
} from './ProgramLogicNodes3';

import {
  CoroutineNode,
  AsyncAwaitNode,
  PromiseChainNode,
  EventLoopNode,
  StateMachineNode,
  GeneratorNode
} from './ProgramLogicNodes4';

import {
  IteratorNode,
  ObserverPatternNode,
  CommandPatternNode,
  StrategyPatternNode,
  FactoryPatternNode,
  SingletonPatternNode
} from './ProgramLogicNodes5';

import {
  DecoratorPatternNode,
  AdapterPatternNode,
  ProxyPatternNode,
  TemplateMethodPatternNode,
  ChainOfResponsibilityNode,
  StatePatternNode,
  VisitorPatternNode,
  MediatorPatternNode
} from './ProgramLogicNodes6';

import { IExecutionContext, DataType } from '../../../core/types';

/**
 * 模拟执行上下文
 */
class MockExecutionContext implements IExecutionContext {
  private inputs = new Map<string, any>();
  private outputs = new Map<string, any>();
  private logs: Array<{ level: string; message: string; data?: any[] }> = [];

  setInputValue(name: string, value: any): void {
    this.inputs.set(name, value);
  }

  getInputValue(name: string): any {
    return this.inputs.get(name);
  }

  setOutputValue(name: string, value: any): void {
    this.outputs.set(name, value);
  }

  getOutputValue(name: string): any {
    return this.outputs.get(name);
  }

  log(level: 'info' | 'warn' | 'error', message: string, ...data: any[]): void {
    this.logs.push({ level, message, data });
    console.log(`[${level.toUpperCase()}] ${message}`, ...data);
  }

  getLogs(): Array<{ level: string; message: string; data?: any[] }> {
    return this.logs;
  }

  clearLogs(): void {
    this.logs = [];
  }

  getOutputs(): Map<string, any> {
    return this.outputs;
  }

  clearOutputs(): void {
    this.outputs.clear();
  }
}

/**
 * 第一批次程序逻辑控制节点测试套件
 */
export class ProgramLogicNodesTestSuite {
  private context: MockExecutionContext;

  constructor() {
    this.context = new MockExecutionContext();
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<{
    passed: number;
    failed: number;
    total: number;
    results: Array<{ name: string; passed: boolean; error?: string }>;
  }> {
    const tests = [
      { name: '基础流程控制节点测试', test: () => this.testBasicFlowControlNodes() },
      { name: '高级流程控制节点测试', test: () => this.testAdvancedFlowControlNodes() },
      { name: '异常处理节点测试', test: () => this.testExceptionHandlingNodes() },
      { name: '函数式编程节点测试', test: () => this.testFunctionalProgrammingNodes() },
      { name: '协程异步节点测试', test: () => this.testCoroutineAsyncNodes() },
      { name: '设计模式节点测试', test: () => this.testDesignPatternNodes() }
    ];

    const results: Array<{ name: string; passed: boolean; error?: string }> = [];
    let passed = 0;
    let failed = 0;

    for (const { name, test } of tests) {
      try {
        await test();
        results.push({ name, passed: true });
        passed++;
        console.log(`✅ ${name} - 通过`);
      } catch (error) {
        results.push({ name, passed: false, error: error instanceof Error ? error.message : String(error) });
        failed++;
        console.error(`❌ ${name} - 失败:`, error);
      }
    }

    return {
      passed,
      failed,
      total: tests.length,
      results
    };
  }

  /**
   * 测试基础流程控制节点 (001-020)
   */
  private async testBasicFlowControlNodes(): Promise<void> {
    console.log('测试基础流程控制节点...');

    // 测试开始节点
    const startNode = new StartNode();
    await startNode.execute(this.context);
    if (!this.context.getOutputValue('exec')) {
      throw new Error('开始节点未正确输出执行信号');
    }

    // 测试条件分支节点
    const branchNode = new BranchNode();
    this.context.setInputValue('exec', true);
    this.context.setInputValue('condition', true);
    this.context.clearOutputs();
    await branchNode.execute(this.context);
    if (!this.context.getOutputValue('true')) {
      throw new Error('条件分支节点未正确处理真值条件');
    }

    // 测试For循环节点
    const forLoopNode = new ForLoopNode();
    this.context.setInputValue('exec', true);
    this.context.setInputValue('startIndex', 0);
    this.context.setInputValue('endIndex', 3);
    this.context.setInputValue('step', 1);
    this.context.clearOutputs();
    await forLoopNode.execute(this.context);
    if (!this.context.getOutputValue('completed')) {
      throw new Error('For循环节点未正确完成执行');
    }

    // 测试延迟节点
    const delayNode = new DelayNode();
    this.context.setInputValue('exec', true);
    this.context.setInputValue('duration', 0.1); // 100ms延迟
    this.context.clearOutputs();
    const startTime = Date.now();
    await delayNode.execute(this.context);
    const endTime = Date.now();
    if (!this.context.getOutputValue('completed') || (endTime - startTime) < 90) {
      throw new Error('延迟节点未正确执行延迟');
    }

    console.log('基础流程控制节点测试通过');
  }

  /**
   * 测试高级流程控制节点 (021-040)
   */
  private async testAdvancedFlowControlNodes(): Promise<void> {
    console.log('测试高级流程控制节点...');

    // 测试并行执行节点
    const parallelNode = new ParallelNode();
    this.context.setInputValue('exec', true);
    this.context.clearOutputs();
    await parallelNode.execute(this.context);
    if (!this.context.getOutputValue('branch1') || 
        !this.context.getOutputValue('branch2') || 
        !this.context.getOutputValue('branch3')) {
      throw new Error('并行执行节点未正确触发所有分支');
    }

    // 测试同步点节点
    const syncNode = new SynchronizationNode();
    this.context.setInputValue('input1', true);
    this.context.setInputValue('input2', true);
    this.context.setInputValue('input3', true);
    this.context.clearOutputs();
    await syncNode.execute(this.context);
    if (!this.context.getOutputValue('completed')) {
      throw new Error('同步点节点未正确完成同步');
    }

    // 测试状态机节点
    const stateMachineNode = new StateMachineNode();
    this.context.setInputValue('transition', true);
    this.context.setInputValue('targetState', 'running');
    this.context.clearOutputs();
    await stateMachineNode.execute(this.context);
    if (this.context.getOutputValue('currentState') !== 'running') {
      throw new Error('状态机节点未正确转换状态');
    }

    console.log('高级流程控制节点测试通过');
  }

  /**
   * 测试异常处理节点
   */
  private async testExceptionHandlingNodes(): Promise<void> {
    console.log('测试异常处理节点...');

    // 测试Try-Catch节点
    const tryCatchNode = new TryCatchNode();
    this.context.setInputValue('exec', true);
    this.context.clearOutputs();
    await tryCatchNode.execute(this.context);
    if (!this.context.getOutputValue('try') || !this.context.getOutputValue('finally')) {
      throw new Error('Try-Catch节点未正确执行try和finally块');
    }

    console.log('异常处理节点测试通过');
  }

  /**
   * 测试函数式编程节点
   */
  private async testFunctionalProgrammingNodes(): Promise<void> {
    console.log('测试函数式编程节点...');

    // 测试函数调用节点
    const functionCallNode = new FunctionCallNode();
    this.context.setInputValue('exec', true);
    this.context.setInputValue('functionName', 'testFunction');
    this.context.setInputValue('param1', 'test');
    this.context.clearOutputs();
    await functionCallNode.execute(this.context);
    if (!this.context.getOutputValue('completed')) {
      throw new Error('函数调用节点未正确完成执行');
    }

    // 测试高阶函数节点
    const higherOrderNode = new HigherOrderFunctionNode();
    this.context.setInputValue('exec', true);
    this.context.setInputValue('function', 'map');
    this.context.setInputValue('data', [1, 2, 3]);
    this.context.clearOutputs();
    await higherOrderNode.execute(this.context);
    if (!this.context.getOutputValue('completed')) {
      throw new Error('高阶函数节点未正确完成执行');
    }

    console.log('函数式编程节点测试通过');
  }

  /**
   * 测试协程异步节点
   */
  private async testCoroutineAsyncNodes(): Promise<void> {
    console.log('测试协程异步节点...');

    // 测试协程节点
    const coroutineNode = new CoroutineNode();
    this.context.setInputValue('start', true);
    this.context.clearOutputs();
    await coroutineNode.execute(this.context);
    if (!this.context.getOutputValue('started')) {
      throw new Error('协程节点未正确启动');
    }

    // 测试生成器节点
    const generatorNode = new GeneratorNode();
    this.context.setInputValue('next', true);
    this.context.setInputValue('maxCount', 5);
    this.context.clearOutputs();
    await generatorNode.execute(this.context);
    if (this.context.getOutputValue('value') === undefined) {
      throw new Error('生成器节点未正确生成值');
    }

    console.log('协程异步节点测试通过');
  }

  /**
   * 测试设计模式节点
   */
  private async testDesignPatternNodes(): Promise<void> {
    console.log('测试设计模式节点...');

    // 测试观察者模式节点
    const observerNode = new ObserverPatternNode();
    this.context.setInputValue('subscribe', true);
    this.context.setInputValue('observerId', 'observer1');
    this.context.clearOutputs();
    await observerNode.execute(this.context);
    if (this.context.getOutputValue('observerCount') !== 1) {
      throw new Error('观察者模式节点未正确添加观察者');
    }

    // 测试工厂模式节点
    const factoryNode = new FactoryPatternNode();
    this.context.setInputValue('create', true);
    this.context.setInputValue('objectType', 'TestObject');
    this.context.clearOutputs();
    await factoryNode.execute(this.context);
    if (!this.context.getOutputValue('created')) {
      throw new Error('工厂模式节点未正确创建对象');
    }

    // 测试单例模式节点
    const singletonNode = new SingletonPatternNode();
    this.context.setInputValue('getInstance', true);
    this.context.setInputValue('instanceKey', 'test');
    this.context.clearOutputs();
    await singletonNode.execute(this.context);
    if (!this.context.getOutputValue('instance')) {
      throw new Error('单例模式节点未正确获取实例');
    }

    console.log('设计模式节点测试通过');
  }
}

/**
 * 运行第一批次程序逻辑控制节点测试
 */
export async function runProgramLogicNodesTest(): Promise<void> {
  console.log('🚀 开始第一批次程序逻辑控制节点测试...');
  
  const testSuite = new ProgramLogicNodesTestSuite();
  const results = await testSuite.runAllTests();
  
  console.log('\n📊 测试结果统计:');
  console.log(`总计: ${results.total} 个测试`);
  console.log(`通过: ${results.passed} 个`);
  console.log(`失败: ${results.failed} 个`);
  console.log(`成功率: ${((results.passed / results.total) * 100).toFixed(1)}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ 失败的测试:');
    results.results.filter(r => !r.passed).forEach(r => {
      console.log(`  - ${r.name}: ${r.error}`);
    });
  } else {
    console.log('\n✅ 所有测试通过！第一批次程序逻辑控制节点实现成功！');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runProgramLogicNodesTest().catch(console.error);
}
