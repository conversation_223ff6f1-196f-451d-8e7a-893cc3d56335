/**
 * 第一批次：程序逻辑控制基础节点导出 (001-050)
 * 统一导出所有程序逻辑控制节点和相关功能
 */

// 导出所有程序逻辑控制节点类
export {
  StartNode,
  EndNode,
  SequenceNode,
  BranchNode,
  SwitchNode,
  LoopControlNode,
  ForLoopNode,
  WhileLoopNode,
  DelayNode,
  GateNode,
  DoOnceNode,
  MultiGateNode
} from './ProgramLogicNodes';

export {
  FlipFlopNode,
  ParallelNode,
  RaceNode,
  SynchronizationNode,
  WaitUntilNode,
  TryCatchNode,
  ThrowExceptionNode,
  RetryNode
} from './ProgramLogicNodes2';

export {
  FunctionCallNode,
  FunctionDefinitionNode,
  ReturnNode,
  RecursiveCallNode,
  HigherOrderFunctionNode,
  ClosureNode,
  CurryingNode,
  PipelineNode,
  FunctionCompositionNode,
  LazyEvaluationNode
} from './ProgramLogicNodes3';

export {
  CoroutineNode,
  AsyncAwaitNode,
  PromiseChainNode,
  EventLoopNode,
  StateMachineNode,
  GeneratorNode
} from './ProgramLogicNodes4';

export {
  IteratorNode,
  ObserverPatternNode,
  CommandPatternNode,
  StrategyPatternNode,
  FactoryPatternNode,
  SingletonPatternNode
} from './ProgramLogicNodes5';

export {
  DecoratorPatternNode,
  AdapterPatternNode,
  ProxyPatternNode,
  TemplateMethodPatternNode,
  ChainOfResponsibilityNode,
  StatePatternNode,
  VisitorPatternNode,
  MediatorPatternNode
} from './ProgramLogicNodes6';

// 导出注册功能
export {
  registerProgramLogicNodes,
  getProgramLogicNodeTypes
} from './ProgramLogicRegistry';

// 导出测试功能
export {
  ProgramLogicNodesTestSuite,
  runProgramLogicNodesTest
} from './ProgramLogicNodesTest';

// 导出节点类型定义
export type { ProgramLogicNodeType } from '../../../components/visual-script/nodes/ProgramLogicNodesIntegration';

/**
 * 第一批次程序逻辑控制节点信息
 */
export const PROGRAM_LOGIC_NODES_INFO = {
  batchNumber: 1,
  nodeRange: '001-050',
  totalNodes: 50,
  categories: [
    '基础流程控制',
    '高级流程控制', 
    '异常处理',
    '函数式编程',
    '协程异步',
    '设计模式'
  ],
  description: '程序逻辑控制基础节点，提供完整的可视化编程逻辑控制能力',
  status: 'completed',
  completedDate: '2025-07-09'
};

/**
 * 获取第一批次程序逻辑控制节点统计信息
 */
export function getProgramLogicNodesStats() {
  return {
    ...PROGRAM_LOGIC_NODES_INFO,
    nodesByCategory: {
      '基础流程控制': 20,  // 001-020
      '高级流程控制': 10,  // 021-030
      '异常处理': 5,       // 031-035
      '函数式编程': 5,     // 036-040
      '协程异步': 5,       // 041-045
      '设计模式': 5        // 046-050
    }
  };
}

/**
 * 验证第一批次程序逻辑控制节点完整性
 */
export function validateProgramLogicNodesCompleteness(): {
  isComplete: boolean;
  missingNodes: string[];
  errors: string[];
} {
  const expectedNodeTypes = [
    'program-logic/start',
    'program-logic/end',
    'program-logic/sequence',
    'program-logic/branch',
    'program-logic/switch',
    'program-logic/loop-control',
    'program-logic/for-loop',
    'program-logic/while-loop',
    'program-logic/delay',
    'program-logic/gate',
    'program-logic/do-once',
    'program-logic/multi-gate',
    'program-logic/flip-flop',
    'program-logic/parallel',
    'program-logic/race',
    'program-logic/synchronization',
    'program-logic/wait-until',
    'program-logic/try-catch',
    'program-logic/throw-exception',
    'program-logic/retry',
    'program-logic/function-call',
    'program-logic/function-definition',
    'program-logic/return',
    'program-logic/recursive-call',
    'program-logic/higher-order-function',
    'program-logic/closure',
    'program-logic/currying',
    'program-logic/pipeline',
    'program-logic/function-composition',
    'program-logic/lazy-evaluation',
    'program-logic/coroutine',
    'program-logic/async-await',
    'program-logic/promise-chain',
    'program-logic/event-loop',
    'program-logic/state-machine',
    'program-logic/generator',
    'program-logic/iterator',
    'program-logic/observer-pattern',
    'program-logic/command-pattern',
    'program-logic/strategy-pattern',
    'program-logic/factory-pattern',
    'program-logic/singleton-pattern',
    'program-logic/decorator-pattern',
    'program-logic/adapter-pattern',
    'program-logic/proxy-pattern',
    'program-logic/template-method-pattern',
    'program-logic/chain-of-responsibility',
    'program-logic/state-pattern',
    'program-logic/visitor-pattern',
    'program-logic/mediator-pattern'
  ];

  const availableNodeTypes = getProgramLogicNodeTypes();
  const missingNodes = expectedNodeTypes.filter(type => !availableNodeTypes.includes(type));
  const errors: string[] = [];

  if (expectedNodeTypes.length !== 50) {
    errors.push(`期望50个节点类型，实际定义了${expectedNodeTypes.length}个`);
  }

  if (availableNodeTypes.length !== expectedNodeTypes.length) {
    errors.push(`注册的节点数量(${availableNodeTypes.length})与期望数量(${expectedNodeTypes.length})不匹配`);
  }

  return {
    isComplete: missingNodes.length === 0 && errors.length === 0,
    missingNodes,
    errors
  };
}

/**
 * 初始化第一批次程序逻辑控制节点
 */
export async function initializeProgramLogicNodes(): Promise<{
  success: boolean;
  message: string;
  stats?: any;
}> {
  try {
    console.log('🚀 初始化第一批次程序逻辑控制节点...');

    // 验证节点完整性
    const validation = validateProgramLogicNodesCompleteness();
    if (!validation.isComplete) {
      return {
        success: false,
        message: `节点完整性验证失败: ${validation.errors.join(', ')}`
      };
    }

    // 注册节点
    registerProgramLogicNodes();

    // 获取统计信息
    const stats = getProgramLogicNodesStats();

    console.log('✅ 第一批次程序逻辑控制节点初始化完成');
    console.log(`📊 统计信息: ${stats.totalNodes}个节点，${stats.categories.length}个分类`);

    return {
      success: true,
      message: '第一批次程序逻辑控制节点初始化成功',
      stats
    };
  } catch (error) {
    console.error('❌ 第一批次程序逻辑控制节点初始化失败:', error);
    return {
      success: false,
      message: `初始化失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 默认导出：第一批次程序逻辑控制节点模块
 */
export default {
  info: PROGRAM_LOGIC_NODES_INFO,
  getStats: getProgramLogicNodesStats,
  validate: validateProgramLogicNodesCompleteness,
  initialize: initializeProgramLogicNodes,
  register: registerProgramLogicNodes,
  test: runProgramLogicNodesTest
};
