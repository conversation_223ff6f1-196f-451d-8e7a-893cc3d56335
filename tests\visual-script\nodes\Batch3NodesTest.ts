/**
 * 第三批次节点测试 (101-150)
 * 测试核心引擎基础节点的功能
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  EngineInitializeNode,
  WorldCreateNode,
  SceneManagerNode,
  SystemRegisterNode,
  TimeManagerNode,
  EventSystemNode,
  MemoryManagerNode
} from '../../../engine/src/visual-script-v2/nodes/core/SystemManagementNodes';

import {
  EntityCreateNode,
  EntityDestroyNode,
  ComponentAddNode,
  ComponentRemoveNode,
  ComponentGetNode,
  EntityQueryNode,
  EntityHierarchyNode
} from '../../../engine/src/visual-script-v2/nodes/core/EntityComponentNodes';

import {
  PositionSetNode,
  RotationSetNode,
  ScaleSetNode,
  TransformMatrixNode,
  CoordinateConvertNode
} from '../../../engine/src/visual-script-v2/nodes/core/TransformNodes';

import { IExecutionContext } from '../../../engine/src/visual-script-v2/core/types';

// 模拟执行上下文
const mockContext: IExecutionContext = {
  variables: new Map(),
  functions: new Map(),
  nodeInstances: new Map(),
  executionId: 'test-execution',
  timestamp: Date.now()
};

describe('第三批次节点测试 - 系统管理节点', () => {
  describe('EngineInitializeNode', () => {
    let node: EngineInitializeNode;

    beforeEach(() => {
      node = new EngineInitializeNode();
    });

    test('应该正确初始化引擎', async () => {
      const config = { debug: true, version: '1.0.0' };
      const plugins = [{ name: 'testPlugin', initialize: jest.fn() }];

      node.setInputValue('config', config);
      node.setInputValue('plugins', plugins);
      node.setInputValue('debug', true);

      await node.execute(mockContext);

      const engine = node.getOutputValue('engine');
      expect(engine).toBeTruthy();
      expect(engine.config).toEqual(config);
      expect(engine.plugins).toEqual(plugins);
      expect(engine.debug).toBe(true);
      expect(engine.systems).toBeInstanceOf(Map);
    });

    test('应该初始化核心系统', async () => {
      await node.execute(mockContext);

      const engine = node.getOutputValue('engine');
      expect(engine.systems.has('time')).toBe(true);
      expect(engine.systems.has('event')).toBe(true);
      expect(engine.systems.has('memory')).toBe(true);
    });
  });

  describe('WorldCreateNode', () => {
    let node: WorldCreateNode;
    let mockEngine: any;

    beforeEach(() => {
      node = new WorldCreateNode();
      mockEngine = {
        worlds: new Map(),
        systems: new Map()
      };
    });

    test('应该正确创建世界', async () => {
      const worldName = 'testWorld';
      const worldConfig = { gravity: -9.8 };

      node.setInputValue('engine', mockEngine);
      node.setInputValue('worldName', worldName);
      node.setInputValue('worldConfig', worldConfig);

      await node.execute(mockContext);

      const world = node.getOutputValue('world');
      expect(world).toBeTruthy();
      expect(world.name).toBe(worldName);
      expect(world.config).toEqual(worldConfig);
      expect(world.entities).toBeInstanceOf(Map);
      expect(mockEngine.worlds.has(worldName)).toBe(true);
    });
  });

  describe('TimeManagerNode', () => {
    let node: TimeManagerNode;
    let mockEngine: any;

    beforeEach(() => {
      node = new TimeManagerNode();
      mockEngine = {
        systems: new Map([
          ['time', {
            startTime: Date.now(),
            currentTime: Date.now(),
            deltaTime: 0,
            timeScale: 1.0,
            frameRate: 60
          }]
        ]),
        frameCount: 0,
        deltaTime: 0,
        lastFrameTime: 0
      };
    });

    test('应该正确更新时间系统', async () => {
      node.setInputValue('engine', mockEngine);
      node.setInputValue('timeScale', 2.0);
      node.setInputValue('targetFrameRate', 30);

      await node.execute(mockContext);

      const deltaTime = node.getOutputValue('deltaTime');
      const frameRate = node.getOutputValue('frameRate');
      
      expect(typeof deltaTime).toBe('number');
      expect(typeof frameRate).toBe('number');
      expect(mockEngine.systems.get('time').timeScale).toBe(2.0);
    });
  });
});

describe('第三批次节点测试 - 实体组件节点', () => {
  describe('EntityCreateNode', () => {
    let node: EntityCreateNode;
    let mockWorld: any;

    beforeEach(() => {
      node = new EntityCreateNode();
      mockWorld = {
        entities: new Map(),
        entityIdCounter: 0
      };
    });

    test('应该正确创建实体', async () => {
      const entityName = 'testEntity';
      const components = [
        { type: 'transform', data: { x: 0, y: 0, z: 0 } },
        { type: 'render', data: { visible: true } }
      ];

      node.setInputValue('world', mockWorld);
      node.setInputValue('entityName', entityName);
      node.setInputValue('components', components);

      await node.execute(mockContext);

      const entity = node.getOutputValue('entity');
      expect(entity).toBeTruthy();
      expect(entity.name).toBe(entityName);
      expect(entity.id).toBe(1);
      expect(entity.components.size).toBe(2);
      expect(entity.components.has('transform')).toBe(true);
      expect(entity.components.has('render')).toBe(true);
      expect(mockWorld.entities.has(1)).toBe(true);
    });

    test('应该设置父子关系', async () => {
      const parentEntity = { id: 1, children: new Set() };
      
      node.setInputValue('world', mockWorld);
      node.setInputValue('entityName', 'childEntity');
      node.setInputValue('parent', parentEntity);

      await node.execute(mockContext);

      const entity = node.getOutputValue('entity');
      expect(entity.parent).toBe(1);
      expect(parentEntity.children.has(entity.id)).toBe(true);
    });
  });

  describe('ComponentAddNode', () => {
    let node: ComponentAddNode;
    let mockEntity: any;

    beforeEach(() => {
      node = new ComponentAddNode();
      mockEntity = {
        id: 1,
        components: new Map()
      };
    });

    test('应该正确添加组件', async () => {
      const componentType = 'physics';
      const componentData = { mass: 1.0, velocity: { x: 0, y: 0, z: 0 } };

      node.setInputValue('entity', mockEntity);
      node.setInputValue('componentType', componentType);
      node.setInputValue('componentData', componentData);

      await node.execute(mockContext);

      const component = node.getOutputValue('component');
      expect(component).toBeTruthy();
      expect(component.type).toBe(componentType);
      expect(component.data).toEqual(componentData);
      expect(mockEntity.components.has(componentType)).toBe(true);
    });
  });

  describe('EntityQueryNode', () => {
    let node: EntityQueryNode;
    let mockWorld: any;

    beforeEach(() => {
      node = new EntityQueryNode();
      mockWorld = {
        entities: new Map([
          [1, { id: 1, isActive: true, components: new Map([['transform', {}], ['render', {}]]) }],
          [2, { id: 2, isActive: true, components: new Map([['transform', {}], ['physics', {}]]) }],
          [3, { id: 3, isActive: false, components: new Map([['transform', {}]]) }]
        ])
      };
    });

    test('应该正确查询具有所有指定组件的实体', async () => {
      node.setInputValue('world', mockWorld);
      node.setInputValue('componentTypes', ['transform', 'render']);
      node.setInputValue('queryType', 'all');

      await node.execute(mockContext);

      const entities = node.getOutputValue('entities');
      const count = node.getOutputValue('count');
      
      expect(entities).toHaveLength(1);
      expect(entities[0].id).toBe(1);
      expect(count).toBe(1);
    });

    test('应该正确查询具有任意指定组件的实体', async () => {
      node.setInputValue('world', mockWorld);
      node.setInputValue('componentTypes', ['render', 'physics']);
      node.setInputValue('queryType', 'any');

      await node.execute(mockContext);

      const entities = node.getOutputValue('entities');
      const count = node.getOutputValue('count');
      
      expect(entities).toHaveLength(2);
      expect(count).toBe(2);
    });
  });
});

describe('第三批次节点测试 - 基础变换节点', () => {
  describe('PositionSetNode', () => {
    let node: PositionSetNode;
    let mockEntity: any;

    beforeEach(() => {
      node = new PositionSetNode();
      mockEntity = {
        id: 1,
        components: new Map()
      };
    });

    test('应该正确设置实体位置', async () => {
      const x = 10, y = 20, z = 30;

      node.setInputValue('entity', mockEntity);
      node.setInputValue('x', x);
      node.setInputValue('y', y);
      node.setInputValue('z', z);
      node.setInputValue('space', 'world');

      await node.execute(mockContext);

      const newPosition = node.getOutputValue('newPosition');
      expect(newPosition).toEqual({ x, y, z });
      
      const transform = mockEntity.components.get('transform');
      expect(transform).toBeTruthy();
      expect(transform.data.worldPosition).toEqual({ x, y, z });
    });

    test('应该使用位置向量设置位置', async () => {
      const position = { x: 5, y: 15, z: 25 };

      node.setInputValue('entity', mockEntity);
      node.setInputValue('position', position);

      await node.execute(mockContext);

      const newPosition = node.getOutputValue('newPosition');
      expect(newPosition).toEqual(position);
    });
  });

  describe('RotationSetNode', () => {
    let node: RotationSetNode;
    let mockEntity: any;

    beforeEach(() => {
      node = new RotationSetNode();
      mockEntity = {
        id: 1,
        components: new Map()
      };
    });

    test('应该正确设置实体旋转', async () => {
      const x = 90, y = 45, z = 0;

      node.setInputValue('entity', mockEntity);
      node.setInputValue('x', x);
      node.setInputValue('y', y);
      node.setInputValue('z', z);
      node.setInputValue('angleMode', 'degrees');

      await node.execute(mockContext);

      const newRotation = node.getOutputValue('newRotation');
      expect(newRotation).toBeTruthy();
      expect(typeof newRotation.x).toBe('number');
      expect(typeof newRotation.y).toBe('number');
      expect(typeof newRotation.z).toBe('number');
      expect(typeof newRotation.w).toBe('number');
      
      const transform = mockEntity.components.get('transform');
      expect(transform).toBeTruthy();
      expect(transform.data.worldRotation).toEqual(newRotation);
    });
  });

  describe('ScaleSetNode', () => {
    let node: ScaleSetNode;
    let mockEntity: any;

    beforeEach(() => {
      node = new ScaleSetNode();
      mockEntity = {
        id: 1,
        components: new Map()
      };
    });

    test('应该正确设置实体缩放', async () => {
      const x = 2, y = 3, z = 1;

      node.setInputValue('entity', mockEntity);
      node.setInputValue('x', x);
      node.setInputValue('y', y);
      node.setInputValue('z', z);

      await node.execute(mockContext);

      const newScale = node.getOutputValue('newScale');
      expect(newScale).toEqual({ x, y, z });
      
      const transform = mockEntity.components.get('transform');
      expect(transform).toBeTruthy();
      expect(transform.data.localScale).toEqual({ x, y, z });
    });

    test('应该正确设置统一缩放', async () => {
      const uniform = 2.5;

      node.setInputValue('entity', mockEntity);
      node.setInputValue('uniform', uniform);

      await node.execute(mockContext);

      const newScale = node.getOutputValue('newScale');
      expect(newScale).toEqual({ x: uniform, y: uniform, z: uniform });
    });
  });

  describe('CoordinateConvertNode', () => {
    let node: CoordinateConvertNode;
    let mockEntity: any;

    beforeEach(() => {
      node = new CoordinateConvertNode();
      mockEntity = {
        id: 1,
        components: new Map([
          ['transform', {
            data: {
              worldPosition: { x: 10, y: 20, z: 30 },
              worldScale: { x: 2, y: 2, z: 2 }
            }
          }]
        ])
      };
    });

    test('应该正确转换本地坐标到世界坐标', async () => {
      const localPoint = { x: 5, y: 10, z: 15 };

      node.setInputValue('entity', mockEntity);
      node.setInputValue('point', localPoint);
      node.setInputValue('fromSpace', 'local');
      node.setInputValue('toSpace', 'world');

      await node.execute(mockContext);

      const convertedPoint = node.getOutputValue('convertedPoint');
      expect(convertedPoint).toEqual({ x: 20, y: 40, z: 60 }); // (5*2+10, 10*2+20, 15*2+30)
    });

    test('应该正确转换世界坐标到本地坐标', async () => {
      const worldPoint = { x: 20, y: 40, z: 60 };

      node.setInputValue('entity', mockEntity);
      node.setInputValue('point', worldPoint);
      node.setInputValue('fromSpace', 'world');
      node.setInputValue('toSpace', 'local');

      await node.execute(mockContext);

      const convertedPoint = node.getOutputValue('convertedPoint');
      expect(convertedPoint).toEqual({ x: 5, y: 10, z: 15 }); // ((20-10)/2, (40-20)/2, (60-30)/2)
    });
  });
});

describe('第三批次节点集成测试', () => {
  test('应该能够组合使用多个节点创建完整的ECS场景', async () => {
    // 创建引擎
    const engineInit = new EngineInitializeNode();
    await engineInit.execute(mockContext);
    const engine = engineInit.getOutputValue('engine');

    // 创建世界
    const worldCreate = new WorldCreateNode();
    worldCreate.setInputValue('engine', engine);
    worldCreate.setInputValue('worldName', 'testWorld');
    await worldCreate.execute(mockContext);
    const world = worldCreate.getOutputValue('world');

    // 创建实体
    const entityCreate = new EntityCreateNode();
    entityCreate.setInputValue('world', world);
    entityCreate.setInputValue('entityName', 'testEntity');
    await entityCreate.execute(mockContext);
    const entity = entityCreate.getOutputValue('entity');

    // 添加变换组件
    const componentAdd = new ComponentAddNode();
    componentAdd.setInputValue('entity', entity);
    componentAdd.setInputValue('componentType', 'transform');
    componentAdd.setInputValue('componentData', {});
    await componentAdd.execute(mockContext);

    // 设置位置
    const positionSet = new PositionSetNode();
    positionSet.setInputValue('entity', entity);
    positionSet.setInputValue('x', 100);
    positionSet.setInputValue('y', 200);
    positionSet.setInputValue('z', 300);
    await positionSet.execute(mockContext);

    // 验证结果
    expect(engine).toBeTruthy();
    expect(world).toBeTruthy();
    expect(entity).toBeTruthy();
    expect(entity.components.has('transform')).toBe(true);
    
    const transform = entity.components.get('transform');
    expect(transform.data.worldPosition).toEqual({ x: 100, y: 200, z: 300 });
  });
});
