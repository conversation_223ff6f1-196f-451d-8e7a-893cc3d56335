/**
 * 第一批次：程序逻辑控制基础节点拖拽组件 (001-050)
 * 提供程序逻辑控制节点的拖拽功能
 */

import React, { useCallback, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Info as InfoIcon,
  Code as CodeIcon,
  PlayArrow as PlayIcon,
  ContentCopy as CopyIcon,
  Bookmark as BookmarkIcon
} from '@mui/icons-material';

import { ProgramLogicNodeType } from './ProgramLogicNodesIntegration';

interface ProgramLogicNodeDragDropProps {
  node: ProgramLogicNodeType;
  onDragStart?: (nodeType: string, nodeData: any) => void;
  onNodeSelect?: (nodeType: string) => void;
  onShowDetails?: (node: ProgramLogicNodeType) => void;
  onAddToFavorites?: (node: ProgramLogicNodeType) => void;
  onCopyNode?: (node: ProgramLogicNodeType) => void;
  isDragging?: boolean;
  isSelected?: boolean;
  isFavorite?: boolean;
}

export const ProgramLogicNodeDragDrop: React.FC<ProgramLogicNodeDragDropProps> = ({
  node,
  onDragStart,
  onNodeSelect,
  onShowDetails,
  onAddToFavorites,
  onCopyNode,
  isDragging = false,
  isSelected = false,
  isFavorite = false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // 处理拖拽开始
  const handleDragStart = useCallback((event: React.DragEvent) => {
    event.dataTransfer.setData('application/json', JSON.stringify({
      nodeType: node.nodeType,
      nodeData: {
        id: node.id,
        name: node.name,
        description: node.description,
        category: node.category,
        color: node.color,
        tags: node.tags,
        inputs: node.inputs,
        outputs: node.outputs
      }
    }));

    if (onDragStart) {
      onDragStart(node.nodeType, {
        id: node.id,
        name: node.name,
        description: node.description,
        category: node.category,
        color: node.color,
        tags: node.tags,
        inputs: node.inputs,
        outputs: node.outputs
      });
    }
  }, [node, onDragStart]);

  // 处理节点点击
  const handleNodeClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    if (onNodeSelect) {
      onNodeSelect(node.nodeType);
    }
  }, [node.nodeType, onNodeSelect]);

  // 处理菜单打开
  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  }, []);

  // 处理菜单关闭
  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  // 处理显示详情
  const handleShowDetails = useCallback(() => {
    handleMenuClose();
    if (onShowDetails) {
      onShowDetails(node);
    }
  }, [node, onShowDetails, handleMenuClose]);

  // 处理添加到收藏
  const handleAddToFavorites = useCallback(() => {
    handleMenuClose();
    if (onAddToFavorites) {
      onAddToFavorites(node);
    }
  }, [node, onAddToFavorites, handleMenuClose]);

  // 处理复制节点
  const handleCopyNode = useCallback(() => {
    handleMenuClose();
    if (onCopyNode) {
      onCopyNode(node);
    }
  }, [node, onCopyNode, handleMenuClose]);

  // 获取节点图标
  const getNodeIcon = useCallback(() => {
    return node.icon || '🔧';
  }, [node.icon]);

  // 获取优先级颜色
  const getPriorityColor = useCallback(() => {
    switch (node.id.substring(0, 1)) {
      case '0': return '#4CAF50'; // 基础流程控制 - 绿色
      case '1': return '#2196F3'; // 高级流程控制 - 蓝色
      case '2': return '#FF9800'; // 异常处理 - 橙色
      case '3': return '#9C27B0'; // 函数式编程 - 紫色
      case '4': return '#F44336'; // 协程异步 - 红色
      default: return '#607D8B'; // 设计模式 - 灰色
    }
  }, [node.id]);

  return (
    <>
      <Card
        sx={{
          cursor: isDragging ? 'grabbing' : 'grab',
          opacity: isDragging ? 0.5 : 1,
          transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
          boxShadow: isSelected ? 3 : (isHovered ? 2 : 1),
          borderLeft: `4px solid ${node.color || getPriorityColor()}`,
          borderColor: isSelected ? 'primary.main' : undefined,
          transition: 'all 0.2s ease-in-out',
          position: 'relative',
          '&:hover': {
            boxShadow: 3
          }
        }}
        draggable
        onDragStart={handleDragStart}
        onClick={handleNodeClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 收藏标记 */}
        {isFavorite && (
          <Box
            sx={{
              position: 'absolute',
              top: 4,
              right: 4,
              color: 'warning.main',
              fontSize: '0.8rem'
            }}
          >
            ⭐
          </Box>
        )}

        <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
          {/* 节点头部 */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, mb: 1 }}>
            {/* 节点图标 */}
            <Box
              sx={{
                fontSize: '1.5rem',
                color: node.color || getPriorityColor(),
                minWidth: 'auto'
              }}
            >
              {getNodeIcon()}
            </Box>

            {/* 节点信息 */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant="subtitle2"
                fontWeight="bold"
                sx={{
                  color: 'text.primary',
                  lineHeight: 1.2,
                  mb: 0.5
                }}
                noWrap
              >
                {node.id} - {node.name}
              </Typography>
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  lineHeight: 1.3
                }}
              >
                {node.description}
              </Typography>
            </Box>

            {/* 菜单按钮 */}
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{
                opacity: isHovered ? 1 : 0,
                transition: 'opacity 0.2s ease-in-out',
                p: 0.5
              }}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* 分类标签 */}
          <Box sx={{ mb: 1 }}>
            <Chip
              label={node.category}
              size="small"
              variant="outlined"
              sx={{
                fontSize: '0.7rem',
                height: 20,
                borderColor: node.color || getPriorityColor(),
                color: node.color || getPriorityColor()
              }}
            />
          </Box>

          {/* 标签 */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {node.tags.slice(0, 3).map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                variant="filled"
                sx={{
                  fontSize: '0.65rem',
                  height: 18,
                  bgcolor: 'action.hover',
                  color: 'text.secondary',
                  '& .MuiChip-label': {
                    px: 0.5
                  }
                }}
              />
            ))}
            {node.tags.length > 3 && (
              <Chip
                label={`+${node.tags.length - 3}`}
                size="small"
                variant="outlined"
                sx={{
                  fontSize: '0.65rem',
                  height: 18,
                  '& .MuiChip-label': {
                    px: 0.5
                  }
                }}
              />
            )}
          </Box>

          {/* 输入输出指示器 */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                输入: {node.inputs.length}
              </Typography>
              <Box
                sx={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  bgcolor: node.inputs.length > 0 ? 'success.main' : 'action.disabled'
                }}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                输出: {node.outputs.length}
              </Typography>
              <Box
                sx={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  bgcolor: node.outputs.length > 0 ? 'info.main' : 'action.disabled'
                }}
              />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* 右键菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <MenuItem onClick={handleShowDetails}>
          <ListItemIcon>
            <InfoIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="查看详情" />
        </MenuItem>
        
        <MenuItem onClick={handleCopyNode}>
          <ListItemIcon>
            <CopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="复制节点" />
        </MenuItem>
        
        <Divider />
        
        <MenuItem onClick={handleAddToFavorites}>
          <ListItemIcon>
            <BookmarkIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary={isFavorite ? "取消收藏" : "添加收藏"} />
        </MenuItem>
      </Menu>
    </>
  );
};

export default ProgramLogicNodeDragDrop;
