/**
 * 第一批次：程序逻辑控制基础节点 (031-050)
 * 完成最后的协程、异步编程和状态机节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 031 - 协程 (Coroutine)
 * 协程执行控制
 */
export class CoroutineNode extends BaseNode {
  private isRunning = false;
  private isPaused = false;

  constructor(id?: string) {
    super(id, 'program-logic/coroutine', '协程', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'start',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '开始协程'
      },
      {
        name: 'pause',
        label: '暂停',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '暂停协程'
      },
      {
        name: 'resume',
        label: '恢复',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '恢复协程'
      },
      {
        name: 'stop',
        label: '停止',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '停止协程'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'coroutineBody',
        label: '协程体',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '协程执行体'
      },
      {
        name: 'started',
        label: '已开始',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '协程开始时触发'
      },
      {
        name: 'paused',
        label: '已暂停',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '协程暂停时触发'
      },
      {
        name: 'resumed',
        label: '已恢复',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '协程恢复时触发'
      },
      {
        name: 'stopped',
        label: '已停止',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '协程停止时触发'
      },
      {
        name: 'isRunning',
        label: '是否运行',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '协程是否正在运行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const startTrigger = context.getInputValue('start');
    const pauseTrigger = context.getInputValue('pause');
    const resumeTrigger = context.getInputValue('resume');
    const stopTrigger = context.getInputValue('stop');

    if (startTrigger && !this.isRunning) {
      this.isRunning = true;
      this.isPaused = false;
      context.setOutputValue('started', true);
      context.setOutputValue('coroutineBody', true);
    }

    if (pauseTrigger && this.isRunning) {
      this.isPaused = true;
      context.setOutputValue('paused', true);
    }

    if (resumeTrigger && this.isRunning && this.isPaused) {
      this.isPaused = false;
      context.setOutputValue('resumed', true);
      context.setOutputValue('coroutineBody', true);
    }

    if (stopTrigger) {
      this.isRunning = false;
      this.isPaused = false;
      context.setOutputValue('stopped', true);
    }

    context.setOutputValue('isRunning', this.isRunning && !this.isPaused);
  }
}

/**
 * 032 - 异步等待 (Async Await)
 * 异步操作等待
 */
export class AsyncAwaitNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/async-await', '异步等待', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始异步操作'
      },
      {
        name: 'asyncOperation',
        label: '异步操作',
        type: DataType.PROMISE,
        direction: 'input',
        required: true,
        description: '要等待的异步操作'
      },
      {
        name: 'timeout',
        label: '超时时间',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 30.0,
        description: '超时时间（秒）'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '异步操作完成'
      },
      {
        name: 'failed',
        label: '失败',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '异步操作失败'
      },
      {
        name: 'timeout',
        label: '超时',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '异步操作超时'
      },
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '异步操作结果'
      },
      {
        name: 'error',
        label: '错误',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '错误信息'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const asyncOperation = context.getInputValue('asyncOperation');
    const timeout = context.getInputValue('timeout') || 30.0;

    try {
      // 模拟异步操作
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout')), timeout * 1000)
      );
      
      const operationPromise = new Promise(resolve => 
        setTimeout(() => resolve('异步操作完成'), 1000)
      );

      const result = await Promise.race([operationPromise, timeoutPromise]);
      
      context.setOutputValue('result', result);
      context.setOutputValue('completed', true);
    } catch (error) {
      if (error instanceof Error && error.message === 'Timeout') {
        context.setOutputValue('timeout', true);
      } else {
        context.setOutputValue('error', error instanceof Error ? error.message : String(error));
        context.setOutputValue('failed', true);
      }
    }
  }
}

/**
 * 033 - Promise链 (Promise Chain)
 * Promise链式调用
 */
export class PromiseChainNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/promise-chain', 'Promise链', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始Promise链'
      },
      {
        name: 'initialValue',
        label: '初始值',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: 'Promise链的初始值'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'then1',
        label: 'Then1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个then处理'
      },
      {
        name: 'then2',
        label: 'Then2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个then处理'
      },
      {
        name: 'then3',
        label: 'Then3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第三个then处理'
      },
      {
        name: 'catch',
        label: 'Catch',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '错误处理'
      },
      {
        name: 'finally',
        label: 'Finally',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '最终处理'
      },
      {
        name: 'result',
        label: '结果',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: 'Promise链结果'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const initialValue = context.getInputValue('initialValue');

    try {
      // 模拟Promise链
      context.setOutputValue('then1', true);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      context.setOutputValue('then2', true);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      context.setOutputValue('then3', true);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      context.setOutputValue('result', `processed_${initialValue}`);
    } catch (error) {
      context.setOutputValue('catch', true);
    } finally {
      context.setOutputValue('finally', true);
    }
  }
}

/**
 * 034 - 事件循环 (Event Loop)
 * 事件循环控制
 */
export class EventLoopNode extends BaseNode {
  private eventQueue: Array<{ name: string; data: any }> = [];
  private isProcessing = false;

  constructor(id?: string) {
    super(id, 'program-logic/event-loop', '事件循环', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'start',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '开始事件循环'
      },
      {
        name: 'stop',
        label: '停止',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '停止事件循环'
      },
      {
        name: 'addEvent',
        label: '添加事件',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加事件到队列'
      },
      {
        name: 'eventName',
        label: '事件名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '事件名称'
      },
      {
        name: 'eventData',
        label: '事件数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '事件数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'processEvent',
        label: '处理事件',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '处理队列中的事件'
      },
      {
        name: 'eventName',
        label: '事件名',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前处理的事件名'
      },
      {
        name: 'eventData',
        label: '事件数据',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '当前处理的事件数据'
      },
      {
        name: 'queueLength',
        label: '队列长度',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '事件队列长度'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const startTrigger = context.getInputValue('start');
    const stopTrigger = context.getInputValue('stop');
    const addEventTrigger = context.getInputValue('addEvent');
    const eventName = context.getInputValue('eventName');
    const eventData = context.getInputValue('eventData');

    if (stopTrigger) {
      this.isProcessing = false;
      return;
    }

    if (addEventTrigger && eventName) {
      this.eventQueue.push({ name: eventName, data: eventData });
    }

    context.setOutputValue('queueLength', this.eventQueue.length);

    if (startTrigger) {
      this.isProcessing = true;
    }

    if (this.isProcessing && this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        context.setOutputValue('eventName', event.name);
        context.setOutputValue('eventData', event.data);
        context.setOutputValue('processEvent', true);
      }
    }
  }
}

/**
 * 035 - 状态机 (State Machine)
 * 有限状态机实现
 */
export class StateMachineNode extends BaseNode {
  private currentState = 'idle';
  private states = new Map<string, any>();

  constructor(id?: string) {
    super(id, 'program-logic/state-machine', '状态机', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'transition',
        label: '状态转换',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '触发状态转换'
      },
      {
        name: 'targetState',
        label: '目标状态',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要转换到的状态'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置到初始状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'stateEnter',
        label: '进入状态',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '进入新状态时触发'
      },
      {
        name: 'stateExit',
        label: '退出状态',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '退出当前状态时触发'
      },
      {
        name: 'currentState',
        label: '当前状态',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前状态名称'
      },
      {
        name: 'previousState',
        label: '前一状态',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '前一个状态名称'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const transitionTrigger = context.getInputValue('transition');
    const targetState = context.getInputValue('targetState');
    const resetTrigger = context.getInputValue('reset');

    if (resetTrigger) {
      const previousState = this.currentState;
      this.currentState = 'idle';
      context.setOutputValue('stateExit', true);
      context.setOutputValue('stateEnter', true);
      context.setOutputValue('previousState', previousState);
    }

    if (transitionTrigger && targetState && targetState !== this.currentState) {
      const previousState = this.currentState;
      context.setOutputValue('stateExit', true);

      this.currentState = targetState;
      context.setOutputValue('stateEnter', true);
      context.setOutputValue('previousState', previousState);
    }

    context.setOutputValue('currentState', this.currentState);
  }
}

/**
 * 036 - 生成器 (Generator)
 * 生成器函数实现
 */
export class GeneratorNode extends BaseNode {
  private currentIndex = 0;
  private isDone = false;

  constructor(id?: string) {
    super(id, 'program-logic/generator', '生成器', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'next',
        label: '下一个',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '获取下一个值'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置生成器'
      },
      {
        name: 'maxCount',
        label: '最大数量',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10,
        description: '最大生成数量'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'value',
        label: '值',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '生成的值'
      },
      {
        name: 'done',
        label: '完成',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否已完成'
      },
      {
        name: 'index',
        label: '索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前索引'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const nextTrigger = context.getInputValue('next');
    const resetTrigger = context.getInputValue('reset');
    const maxCount = context.getInputValue('maxCount') || 10;

    if (resetTrigger) {
      this.currentIndex = 0;
      this.isDone = false;
    }

    if (nextTrigger && !this.isDone) {
      if (this.currentIndex < maxCount) {
        context.setOutputValue('value', `item_${this.currentIndex}`);
        context.setOutputValue('index', this.currentIndex);
        this.currentIndex++;
      } else {
        this.isDone = true;
      }
    }

    context.setOutputValue('done', this.isDone);
  }
}
