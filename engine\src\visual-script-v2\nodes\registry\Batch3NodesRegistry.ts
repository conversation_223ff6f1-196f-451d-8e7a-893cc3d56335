/**
 * 第三批次节点注册表 (101-150)
 * 核心引擎基础节点注册
 */

import { NodeRegistry } from './NodeRegistry';
import { NodeDefinition, NodeCategory, DataType } from '../../core/types';

// 导入系统管理节点 (101-120)
import {
  EngineInitializeNode,
  WorldCreateNode,
  SceneManagerNode,
  SystemRegisterNode,
  TimeManagerNode,
  EventSystemNode,
  MemoryManagerNode
} from '../core/SystemManagementNodes';

// 导入实体组件节点 (121-140)
import {
  EntityCreateNode,
  EntityDestroyNode,
  ComponentAddNode,
  ComponentRemoveNode,
  ComponentGetNode,
  EntityQueryNode,
  EntityHierarchyNode
} from '../core/EntityComponentNodes';

// 导入基础变换节点 (141-150)
import {
  PositionSetNode,
  RotationSetNode,
  ScaleSetNode,
  TransformMatrixNode,
  CoordinateConvertNode
} from '../core/TransformNodes';

/**
 * 第三批次节点注册表类
 */
export class Batch3NodesRegistry {
  private static instance: Batch3NodesRegistry;
  private registry: NodeRegistry;
  private registeredNodes: Set<string> = new Set();

  private constructor() {
    this.registry = NodeRegistry.getInstance();
  }

  public static getInstance(): Batch3NodesRegistry {
    if (!Batch3NodesRegistry.instance) {
      Batch3NodesRegistry.instance = new Batch3NodesRegistry();
    }
    return Batch3NodesRegistry.instance;
  }

  /**
   * 注册所有第三批次节点
   */
  public registerAllNodes(): void {
    console.log('开始注册第三批次节点 (101-150)...');

    // 注册系统管理节点 (101-120)
    this.registerSystemManagementNodes();

    // 注册实体组件节点 (121-140)
    this.registerEntityComponentNodes();

    // 注册基础变换节点 (141-150)
    this.registerTransformNodes();

    console.log(`第三批次节点注册完成，共注册 ${this.registeredNodes.size} 个节点`);
  }

  /**
   * 注册系统管理节点 (101-120)
   */
  private registerSystemManagementNodes(): void {
    const systemNodes = [
      {
        nodeClass: EngineInitializeNode,
        definition: {
          type: 'EngineInitialize',
          name: '引擎初始化',
          category: NodeCategory.CORE,
          description: '初始化引擎系统',
          tags: ['引擎', '初始化', '系统'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: WorldCreateNode,
        definition: {
          type: 'WorldCreate',
          name: '世界创建',
          category: NodeCategory.CORE,
          description: '创建游戏世界实例',
          tags: ['世界', '创建', '实例'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: SceneManagerNode,
        definition: {
          type: 'SceneManager',
          name: '场景管理',
          category: NodeCategory.CORE,
          description: '场景加载和切换管理',
          tags: ['场景', '管理', '加载'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: SystemRegisterNode,
        definition: {
          type: 'SystemRegister',
          name: '系统注册',
          category: NodeCategory.CORE,
          description: '注册和管理系统组件',
          tags: ['系统', '注册', '组件'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: TimeManagerNode,
        definition: {
          type: 'TimeManager',
          name: '时间管理',
          category: NodeCategory.CORE,
          description: '游戏时间和帧率控制',
          tags: ['时间', '帧率', '控制'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: EventSystemNode,
        definition: {
          type: 'EventSystem',
          name: '事件系统',
          category: NodeCategory.CORE,
          description: '事件发布和订阅管理',
          tags: ['事件', '发布', '订阅'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MemoryManagerNode,
        definition: {
          type: 'MemoryManager',
          name: '内存管理',
          category: NodeCategory.CORE,
          description: '内存分配和垃圾回收管理',
          tags: ['内存', '分配', '垃圾回收'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup(systemNodes, '系统管理节点');
  }

  /**
   * 注册实体组件节点 (121-140)
   */
  private registerEntityComponentNodes(): void {
    const entityNodes = [
      {
        nodeClass: EntityCreateNode,
        definition: {
          type: 'EntityCreate',
          name: '实体创建',
          category: NodeCategory.CORE,
          description: '创建新的实体',
          tags: ['实体', '创建', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: EntityDestroyNode,
        definition: {
          type: 'EntityDestroy',
          name: '实体销毁',
          category: NodeCategory.CORE,
          description: '销毁实体及其组件',
          tags: ['实体', '销毁', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ComponentAddNode,
        definition: {
          type: 'ComponentAdd',
          name: '组件添加',
          category: NodeCategory.CORE,
          description: '向实体添加组件',
          tags: ['组件', '添加', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ComponentRemoveNode,
        definition: {
          type: 'ComponentRemove',
          name: '组件移除',
          category: NodeCategory.CORE,
          description: '从实体移除组件',
          tags: ['组件', '移除', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ComponentGetNode,
        definition: {
          type: 'ComponentGet',
          name: '组件获取',
          category: NodeCategory.CORE,
          description: '获取实体的组件',
          tags: ['组件', '获取', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: EntityQueryNode,
        definition: {
          type: 'EntityQuery',
          name: '实体查询',
          category: NodeCategory.CORE,
          description: '根据组件类型查询实体',
          tags: ['实体', '查询', 'ECS'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: EntityHierarchyNode,
        definition: {
          type: 'EntityHierarchy',
          name: '实体层级',
          category: NodeCategory.CORE,
          description: '管理实体的父子关系',
          tags: ['实体', '层级', '父子关系'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup(entityNodes, '实体组件节点');
  }

  /**
   * 注册基础变换节点 (141-150)
   */
  private registerTransformNodes(): void {
    const transformNodes = [
      {
        nodeClass: PositionSetNode,
        definition: {
          type: 'PositionSet',
          name: '位置设置',
          category: NodeCategory.CORE,
          description: '设置实体的位置',
          tags: ['位置', '设置', '变换'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: RotationSetNode,
        definition: {
          type: 'RotationSet',
          name: '旋转设置',
          category: NodeCategory.CORE,
          description: '设置实体的旋转',
          tags: ['旋转', '设置', '变换'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ScaleSetNode,
        definition: {
          type: 'ScaleSet',
          name: '缩放设置',
          category: NodeCategory.CORE,
          description: '设置实体的缩放',
          tags: ['缩放', '设置', '变换'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: TransformMatrixNode,
        definition: {
          type: 'TransformMatrix',
          name: '变换矩阵',
          category: NodeCategory.CORE,
          description: '计算和应用变换矩阵',
          tags: ['矩阵', '变换', '计算'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoordinateConvertNode,
        definition: {
          type: 'CoordinateConvert',
          name: '坐标转换',
          category: NodeCategory.CORE,
          description: '世界坐标和本地坐标转换',
          tags: ['坐标', '转换', '空间'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup(transformNodes, '基础变换节点');
  }

  /**
   * 注册节点组
   */
  private registerNodeGroup(nodes: any[], groupName: string): void {
    console.log(`注册${groupName}...`);
    
    for (const { nodeClass, definition } of nodes) {
      try {
        this.registry.register({
          ...definition,
          factory: () => new nodeClass()
        });
        
        this.registeredNodes.add(definition.type);
        console.log(`✓ 已注册: ${definition.name} (${definition.type})`);
      } catch (error) {
        console.error(`✗ 注册失败: ${definition.name}`, error);
      }
    }
  }

  /**
   * 获取已注册的节点列表
   */
  public getRegisteredNodes(): string[] {
    return Array.from(this.registeredNodes);
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }
}
