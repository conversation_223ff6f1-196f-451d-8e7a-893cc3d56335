/**
 * 第二批次节点测试 (051-100)
 * 测试程序逻辑控制高级节点的功能
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  ErrorBoundaryNode,
  CircuitBreakerNode,
  TimeoutHandlerNode,
  ResourceCleanupNode
} from '../../../engine/src/visual-script-v2/nodes/logic/ExceptionHandlingNodes';

import {
  FunctionDefinitionNode,
  FunctionCallNode,
  MethodCallNode,
  ClosureNode,
  CurryingNode,
  MemoizationNode,
  DebounceNode,
  ThrottleNode
} from '../../../engine/src/visual-script-v2/nodes/logic/FunctionMethodNodes';

import {
  CoroutineCreateNode,
  AsyncFunctionNode,
  AwaitNode,
  PromiseCreateNode,
  PromiseAllNode,
  TaskQueueNode
} from '../../../engine/src/visual-script-v2/nodes/logic/CoroutineAsyncNodes';

import { IExecutionContext } from '../../../engine/src/visual-script-v2/core/types';

// 模拟执行上下文
const mockContext: IExecutionContext = {
  variables: new Map(),
  functions: new Map(),
  nodeInstances: new Map(),
  executionId: 'test-execution',
  timestamp: Date.now()
};

describe('第二批次节点测试 - 异常处理节点', () => {
  describe('ErrorBoundaryNode', () => {
    let node: ErrorBoundaryNode;

    beforeEach(() => {
      node = new ErrorBoundaryNode();
    });

    test('应该正确处理正常执行', async () => {
      node.setInputValue('input', () => Promise.resolve('success'));
      
      await node.execute(mockContext);
      
      expect(node.getOutputValue('success')).toBe(true);
      expect(node.getOutputValue('error')).toBeNull();
    });

    test('应该正确捕获和处理错误', async () => {
      const errorHandler = jest.fn();
      node.setInputValue('input', () => { throw new Error('test error'); });
      node.setInputValue('errorHandler', errorHandler);
      node.setInputValue('fallback', 'fallback value');

      try {
        await node.execute(mockContext);
      } catch (error) {
        // 错误应该被捕获
      }

      expect(node.getOutputValue('success')).toBe(false);
      expect(node.getOutputValue('error')).toBeTruthy();
      expect(errorHandler).toHaveBeenCalled();
    });
  });

  describe('CircuitBreakerNode', () => {
    let node: CircuitBreakerNode;

    beforeEach(() => {
      node = new CircuitBreakerNode();
    });

    test('应该在正常情况下允许执行', async () => {
      node.setInputValue('input', () => Promise.resolve('success'));
      node.setInputValue('failureThreshold', 3);

      await node.execute(mockContext);

      expect(node.getOutputValue('state')).toBe('CLOSED');
      expect(node.getOutputValue('failureCount')).toBe(0);
    });

    test('应该在达到失败阈值后开启断路器', async () => {
      node.setInputValue('input', () => { throw new Error('failure'); });
      node.setInputValue('failureThreshold', 2);

      // 第一次失败
      try {
        await node.execute(mockContext);
      } catch (error) {}

      // 第二次失败，应该开启断路器
      try {
        await node.execute(mockContext);
      } catch (error) {}

      expect(node.getOutputValue('state')).toBe('OPEN');
    });
  });

  describe('TimeoutHandlerNode', () => {
    let node: TimeoutHandlerNode;

    beforeEach(() => {
      node = new TimeoutHandlerNode();
    });

    test('应该正确处理正常执行', async () => {
      node.setInputValue('input', () => new Promise(resolve => setTimeout(() => resolve('success'), 100)));
      node.setInputValue('timeout', 1000);

      await node.execute(mockContext);

      expect(node.getOutputValue('duration')).toBeGreaterThan(90);
      expect(node.getOutputValue('duration')).toBeLessThan(200);
    });

    test('应该正确处理超时情况', async () => {
      const onTimeout = jest.fn();
      node.setInputValue('input', () => new Promise(resolve => setTimeout(() => resolve('success'), 1000)));
      node.setInputValue('timeout', 100);
      node.setInputValue('onTimeout', onTimeout);

      try {
        await node.execute(mockContext);
      } catch (error) {
        expect(error.message).toBe('操作超时');
      }

      expect(onTimeout).toHaveBeenCalled();
    });
  });
});

describe('第二批次节点测试 - 函数和方法节点', () => {
  describe('FunctionDefinitionNode', () => {
    let node: FunctionDefinitionNode;

    beforeEach(() => {
      node = new FunctionDefinitionNode();
    });

    test('应该正确定义函数', async () => {
      const functionBody = (params: any) => params.a + params.b;
      
      node.setInputValue('name', 'add');
      node.setInputValue('parameters', ['a', 'b']);
      node.setInputValue('body', functionBody);

      await node.execute(mockContext);

      const definedFunction = node.getOutputValue('function');
      expect(typeof definedFunction).toBe('function');
      expect(definedFunction.name).toBe('add');
    });
  });

  describe('FunctionCallNode', () => {
    let node: FunctionCallNode;

    beforeEach(() => {
      node = new FunctionCallNode();
    });

    test('应该正确调用函数', async () => {
      const testFunction = (a: number, b: number) => a + b;
      
      node.setInputValue('function', testFunction);
      node.setInputValue('arguments', [3, 5]);

      await node.execute(mockContext);

      expect(node.getOutputValue('result')).toBe(8);
      expect(node.getOutputValue('error')).toBeNull();
    });

    test('应该正确处理函数调用错误', async () => {
      const errorFunction = () => { throw new Error('function error'); };
      
      node.setInputValue('function', errorFunction);
      node.setInputValue('arguments', []);

      try {
        await node.execute(mockContext);
      } catch (error) {
        expect(error.message).toBe('function error');
      }

      expect(node.getOutputValue('result')).toBeNull();
      expect(node.getOutputValue('error')).toBeTruthy();
    });
  });

  describe('CurryingNode', () => {
    let node: CurryingNode;

    beforeEach(() => {
      node = new CurryingNode();
    });

    test('应该正确实现柯里化', async () => {
      const add = (a: number, b: number, c: number) => a + b + c;
      
      node.setInputValue('function', add);
      node.setInputValue('arity', 3);
      node.setInputValue('partialArgs', [1]);

      await node.execute(mockContext);

      const curriedFunction = node.getOutputValue('curriedFunction');
      expect(typeof curriedFunction).toBe('function');
      expect(node.getOutputValue('isComplete')).toBe(false);

      // 测试柯里化函数
      const result = curriedFunction(2)(3);
      expect(result).toBe(6);
    });
  });

  describe('MemoizationNode', () => {
    let node: MemoizationNode;

    beforeEach(() => {
      node = new MemoizationNode();
    });

    test('应该正确实现记忆化', async () => {
      let callCount = 0;
      const expensiveFunction = (n: number) => {
        callCount++;
        return n * n;
      };

      node.setInputValue('function', expensiveFunction);
      node.setInputValue('arguments', [5]);

      // 第一次调用
      await node.execute(mockContext);
      expect(node.getOutputValue('result')).toBe(25);
      expect(node.getOutputValue('cacheHit')).toBe(false);
      expect(callCount).toBe(1);

      // 第二次调用相同参数
      await node.execute(mockContext);
      expect(node.getOutputValue('result')).toBe(25);
      expect(node.getOutputValue('cacheHit')).toBe(true);
      expect(callCount).toBe(1); // 函数不应该再次被调用
    });
  });

  describe('DebounceNode', () => {
    let node: DebounceNode;

    beforeEach(() => {
      node = new DebounceNode();
    });

    test('应该正确实现防抖', async () => {
      let callCount = 0;
      const testFunction = () => {
        callCount++;
        return 'executed';
      };

      node.setInputValue('function', testFunction);
      node.setInputValue('delay', 100);
      node.setInputValue('arguments', []);

      await node.execute(mockContext);

      const debouncedFunction = node.getOutputValue('debouncedFunction');
      
      // 快速连续调用
      debouncedFunction();
      debouncedFunction();
      debouncedFunction();

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(callCount).toBe(1); // 只应该执行一次
    });
  });
});

describe('第二批次节点测试 - 协程和异步节点', () => {
  describe('AsyncFunctionNode', () => {
    let node: AsyncFunctionNode;

    beforeEach(() => {
      node = new AsyncFunctionNode();
    });

    test('应该正确执行异步函数', async () => {
      const asyncFunction = async (value: string) => {
        await new Promise(resolve => setTimeout(resolve, 50));
        return `async: ${value}`;
      };

      node.setInputValue('asyncFunction', asyncFunction);
      node.setInputValue('arguments', ['test']);
      node.setInputValue('timeout', 1000);

      await node.execute(mockContext);

      expect(node.getOutputValue('result')).toBe('async: test');
      expect(node.getOutputValue('error')).toBeNull();
    });

    test('应该正确处理异步函数超时', async () => {
      const slowAsyncFunction = async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return 'slow result';
      };

      node.setInputValue('asyncFunction', slowAsyncFunction);
      node.setInputValue('timeout', 100);

      try {
        await node.execute(mockContext);
      } catch (error) {
        // 超时应该被处理
      }

      // 应该触发超时输出
    });
  });

  describe('PromiseCreateNode', () => {
    let node: PromiseCreateNode;

    beforeEach(() => {
      node = new PromiseCreateNode();
    });

    test('应该正确创建自动解决的Promise', async () => {
      node.setInputValue('autoResolve', true);
      node.setInputValue('resolveValue', 'test value');
      node.setInputValue('delay', 50);

      await node.execute(mockContext);

      const promise = node.getOutputValue('promise');
      expect(promise).toBeInstanceOf(Promise);

      const result = await promise;
      expect(result).toBe('test value');
    });

    test('应该正确创建自定义执行器的Promise', async () => {
      const executor = (resolve: Function, reject: Function) => {
        setTimeout(() => resolve('custom result'), 50);
      };

      node.setInputValue('executor', executor);

      await node.execute(mockContext);

      const promise = node.getOutputValue('promise');
      const result = await promise;
      expect(result).toBe('custom result');
    });
  });

  describe('PromiseAllNode', () => {
    let node: PromiseAllNode;

    beforeEach(() => {
      node = new PromiseAllNode();
    });

    test('应该正确并行执行多个Promise', async () => {
      const promises = [
        Promise.resolve(1),
        Promise.resolve(2),
        Promise.resolve(3)
      ];

      node.setInputValue('promises', promises);
      node.setInputValue('failFast', true);

      await node.execute(mockContext);

      const results = node.getOutputValue('results');
      expect(results).toEqual([1, 2, 3]);
    });

    test('应该正确处理Promise失败', async () => {
      const promises = [
        Promise.resolve(1),
        Promise.reject(new Error('test error')),
        Promise.resolve(3)
      ];

      node.setInputValue('promises', promises);
      node.setInputValue('failFast', true);

      try {
        await node.execute(mockContext);
      } catch (error) {
        expect(error.message).toBe('test error');
      }

      expect(node.getOutputValue('results')).toBeNull();
      expect(node.getOutputValue('error')).toBeTruthy();
    });
  });

  describe('TaskQueueNode', () => {
    let node: TaskQueueNode;

    beforeEach(() => {
      node = new TaskQueueNode();
    });

    test('应该正确管理任务队列', async () => {
      let executedTasks: string[] = [];
      
      const task1 = async () => {
        executedTasks.push('task1');
      };
      
      const task2 = async () => {
        executedTasks.push('task2');
      };

      // 添加第一个任务
      node.setInputValue('task', task1);
      node.setInputValue('taskId', 'task1');
      node.setInputValue('priority', 1);
      await node.execute(mockContext);

      // 添加第二个任务
      node.setInputValue('task', task2);
      node.setInputValue('taskId', 'task2');
      node.setInputValue('priority', 2);
      await node.execute(mockContext);

      // 等待任务执行
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(executedTasks).toContain('task1');
      expect(executedTasks).toContain('task2');
      expect(node.getOutputValue('queueSize')).toBe(0);
    });
  });
});

describe('第二批次节点集成测试', () => {
  test('应该能够组合使用多个节点', async () => {
    // 创建一个复杂的节点组合测试
    const functionDef = new FunctionDefinitionNode();
    const functionCall = new FunctionCallNode();
    const errorBoundary = new ErrorBoundaryNode();

    // 定义一个可能出错的函数
    const riskyFunction = (value: number) => {
      if (value < 0) {
        throw new Error('负数不被允许');
      }
      return value * 2;
    };

    // 设置函数定义节点
    functionDef.setInputValue('name', 'riskyFunction');
    functionDef.setInputValue('parameters', ['value']);
    functionDef.setInputValue('body', riskyFunction);

    await functionDef.execute(mockContext);

    const definedFunction = functionDef.getOutputValue('function');

    // 设置函数调用节点
    functionCall.setInputValue('function', definedFunction);
    functionCall.setInputValue('arguments', [5]);

    // 在错误边界中执行函数调用
    errorBoundary.setInputValue('input', () => functionCall.execute(mockContext));
    errorBoundary.setInputValue('fallback', 0);

    await errorBoundary.execute(mockContext);

    expect(errorBoundary.getOutputValue('success')).toBe(true);
    expect(functionCall.getOutputValue('result')).toBe(10);
  });
});
