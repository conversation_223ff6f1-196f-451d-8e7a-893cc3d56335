/**
 * 第二批次节点注册表 (051-100)
 * 程序逻辑控制高级节点注册
 */

import { NodeRegistry } from './NodeRegistry';
import { NodeDefinition, NodeCategory, DataType } from '../../core/types';

// 导入异常处理节点 (051-060)
import {
  ErrorBoundaryNode,
  CircuitBreakerNode,
  TimeoutHandlerNode,
  ResourceCleanupNode,
  MemoryLeakDetectionNode,
  DeadlockDetectionNode,
  PerformanceMonitorNode,
  HealthCheckNode,
  FailoverNode,
  DisasterRecoveryNode
} from '../logic/ExceptionHandlingNodes';

// 导入函数和方法节点 (061-080)
import {
  FunctionDefinitionNode,
  FunctionCallNode,
  MethodCallNode,
  StaticMethodCallNode,
  ConstructorCallNode,
  DestructorCallNode,
  CallbackFunctionNode,
  AnonymousFunctionNode,
  ArrowFunctionNode,
  HigherOrderFunctionNode,
  ClosureNode,
  CurryingNode,
  PartialFunctionNode,
  FunctionCompositionNode,
  FunctionPipelineNode,
  MemoizationNode,
  DebounceNode,
  ThrottleNode,
  RecursiveCallNode,
  TailRecursionNode
} from '../logic/FunctionMethodNodes';

// 导入协程和异步节点 (081-100)
import {
  CoroutineCreateNode,
  CoroutineStartNode,
  CoroutineYieldNode,
  CoroutineResumeNode,
  CoroutineWaitNode,
  CoroutineCancelNode,
  AsyncFunctionNode,
  AwaitNode,
  PromiseCreateNode,
  PromiseResolveNode,
  PromiseRejectNode,
  PromiseChainNode,
  PromiseAllNode,
  PromiseRaceNode,
  AsyncIteratorNode,
  AsyncGeneratorNode,
  TaskQueueNode,
  EventLoopNode,
  MicrotaskNode,
  MacrotaskNode
} from '../logic/CoroutineAsyncNodes';

/**
 * 第二批次节点注册表类
 */
export class Batch2NodesRegistry {
  private static instance: Batch2NodesRegistry;
  private registry: NodeRegistry;
  private registeredNodes: Set<string> = new Set();

  private constructor() {
    this.registry = NodeRegistry.getInstance();
  }

  public static getInstance(): Batch2NodesRegistry {
    if (!Batch2NodesRegistry.instance) {
      Batch2NodesRegistry.instance = new Batch2NodesRegistry();
    }
    return Batch2NodesRegistry.instance;
  }

  /**
   * 注册所有第二批次节点
   */
  public registerAllNodes(): void {
    console.log('开始注册第二批次节点 (051-100)...');

    // 注册异常处理节点 (051-060)
    this.registerExceptionHandlingNodes();

    // 注册函数和方法节点 (061-080)
    this.registerFunctionMethodNodes();

    // 注册协程和异步节点 (081-100)
    this.registerCoroutineAsyncNodes();

    console.log(`第二批次节点注册完成，共注册 ${this.registeredNodes.size} 个节点`);
  }

  /**
   * 注册异常处理节点 (051-060)
   */
  private registerExceptionHandlingNodes(): void {
    const exceptionNodes = [
      {
        nodeClass: ErrorBoundaryNode,
        definition: {
          type: 'ErrorBoundary',
          name: '错误边界',
          category: NodeCategory.LOGIC,
          description: '提供错误边界保护，防止错误传播',
          tags: ['异常处理', '错误边界', '容错'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CircuitBreakerNode,
        definition: {
          type: 'CircuitBreaker',
          name: '断路器',
          category: NodeCategory.LOGIC,
          description: '实现断路器模式，防止级联故障',
          tags: ['异常处理', '断路器', '故障保护'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: TimeoutHandlerNode,
        definition: {
          type: 'TimeoutHandler',
          name: '超时处理',
          category: NodeCategory.LOGIC,
          description: '处理操作超时异常',
          tags: ['异常处理', '超时', '时间控制'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ResourceCleanupNode,
        definition: {
          type: 'ResourceCleanup',
          name: '资源清理',
          category: NodeCategory.LOGIC,
          description: '自动管理资源的创建和清理',
          tags: ['异常处理', '资源管理', '清理'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MemoryLeakDetectionNode,
        definition: {
          type: 'MemoryLeakDetection',
          name: '内存泄漏检测',
          category: NodeCategory.LOGIC,
          description: '监控内存使用情况，检测潜在的内存泄漏',
          tags: ['异常处理', '内存监控', '性能'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: DeadlockDetectionNode,
        definition: {
          type: 'DeadlockDetection',
          name: '死锁检测',
          category: NodeCategory.LOGIC,
          description: '检测和处理死锁情况',
          tags: ['异常处理', '死锁检测', '并发'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PerformanceMonitorNode,
        definition: {
          type: 'PerformanceMonitor',
          name: '性能监控',
          category: NodeCategory.LOGIC,
          description: '监控系统性能指标，检测性能异常',
          tags: ['异常处理', '性能监控', '系统监控'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: HealthCheckNode,
        definition: {
          type: 'HealthCheck',
          name: '健康检查',
          category: NodeCategory.LOGIC,
          description: '执行系统健康检查，确保系统正常运行',
          tags: ['异常处理', '健康检查', '系统监控'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: FailoverNode,
        definition: {
          type: 'Failover',
          name: '故障转移',
          category: NodeCategory.LOGIC,
          description: '实现故障转移机制，在主服务失败时切换到备用服务',
          tags: ['异常处理', '故障转移', '高可用'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: DisasterRecoveryNode,
        definition: {
          type: 'DisasterRecovery',
          name: '灾难恢复',
          category: NodeCategory.LOGIC,
          description: '实现灾难恢复机制，在系统发生严重故障时进行恢复',
          tags: ['异常处理', '灾难恢复', '系统恢复'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup(exceptionNodes, '异常处理节点');
  }

  /**
   * 注册函数和方法节点 (061-080)
   */
  private registerFunctionMethodNodes(): void {
    const functionNodes = [
      {
        nodeClass: FunctionDefinitionNode,
        definition: {
          type: 'FunctionDefinition',
          name: '函数定义',
          category: NodeCategory.LOGIC,
          description: '定义自定义函数',
          tags: ['函数', '定义', '自定义'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: FunctionCallNode,
        definition: {
          type: 'FunctionCall',
          name: '函数调用',
          category: NodeCategory.LOGIC,
          description: '调用函数',
          tags: ['函数', '调用', '执行'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MethodCallNode,
        definition: {
          type: 'MethodCall',
          name: '方法调用',
          category: NodeCategory.LOGIC,
          description: '调用对象方法',
          tags: ['方法', '调用', '对象'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: StaticMethodCallNode,
        definition: {
          type: 'StaticMethodCall',
          name: '静态方法调用',
          category: NodeCategory.LOGIC,
          description: '调用静态方法',
          tags: ['静态方法', '调用', '类'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ConstructorCallNode,
        definition: {
          type: 'ConstructorCall',
          name: '构造函数调用',
          category: NodeCategory.LOGIC,
          description: '调用构造函数创建对象实例',
          tags: ['构造函数', '实例化', '对象'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: DestructorCallNode,
        definition: {
          type: 'DestructorCall',
          name: '析构函数调用',
          category: NodeCategory.LOGIC,
          description: '调用析构函数清理资源',
          tags: ['析构函数', '清理', '资源'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CallbackFunctionNode,
        definition: {
          type: 'CallbackFunction',
          name: '回调函数',
          category: NodeCategory.LOGIC,
          description: '处理回调函数',
          tags: ['回调', '异步', '事件'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: AnonymousFunctionNode,
        definition: {
          type: 'AnonymousFunction',
          name: '匿名函数',
          category: NodeCategory.LOGIC,
          description: '创建匿名函数',
          tags: ['匿名函数', '动态', '代码'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ArrowFunctionNode,
        definition: {
          type: 'ArrowFunction',
          name: '箭头函数',
          category: NodeCategory.LOGIC,
          description: '创建箭头函数',
          tags: ['箭头函数', 'ES6', '简洁'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: HigherOrderFunctionNode,
        definition: {
          type: 'HigherOrderFunction',
          name: '高阶函数',
          category: NodeCategory.LOGIC,
          description: '处理高阶函数（接受函数作为参数或返回函数的函数）',
          tags: ['高阶函数', '函数式编程', '抽象'],
          version: '1.0.0'
        }
      }
    ];

    // 添加更多函数节点
    const moreFunctionNodes = [
      {
        nodeClass: ClosureNode,
        definition: {
          type: 'Closure',
          name: '闭包',
          category: NodeCategory.LOGIC,
          description: '创建和管理闭包',
          tags: ['闭包', '作用域', '变量捕获'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CurryingNode,
        definition: {
          type: 'Currying',
          name: '柯里化',
          category: NodeCategory.LOGIC,
          description: '实现函数柯里化',
          tags: ['柯里化', '函数式编程', '参数'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PartialFunctionNode,
        definition: {
          type: 'PartialFunction',
          name: '偏函数',
          category: NodeCategory.LOGIC,
          description: '实现偏函数应用',
          tags: ['偏函数', '参数绑定', '函数式编程'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: FunctionCompositionNode,
        definition: {
          type: 'FunctionComposition',
          name: '函数组合',
          category: NodeCategory.LOGIC,
          description: '实现函数组合',
          tags: ['函数组合', '管道', '链式'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: FunctionPipelineNode,
        definition: {
          type: 'FunctionPipeline',
          name: '函数管道',
          category: NodeCategory.LOGIC,
          description: '实现函数管道处理',
          tags: ['管道', '流水线', '数据处理'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MemoizationNode,
        definition: {
          type: 'Memoization',
          name: '记忆化',
          category: NodeCategory.LOGIC,
          description: '实现函数记忆化优化',
          tags: ['记忆化', '缓存', '性能优化'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: DebounceNode,
        definition: {
          type: 'Debounce',
          name: '防抖',
          category: NodeCategory.LOGIC,
          description: '实现函数防抖',
          tags: ['防抖', '延迟执行', '性能优化'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: ThrottleNode,
        definition: {
          type: 'Throttle',
          name: '节流',
          category: NodeCategory.LOGIC,
          description: '实现函数节流',
          tags: ['节流', '限制频率', '性能优化'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: RecursiveCallNode,
        definition: {
          type: 'RecursiveCall',
          name: '递归调用',
          category: NodeCategory.LOGIC,
          description: '实现递归函数调用',
          tags: ['递归', '算法', '循环'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: TailRecursionNode,
        definition: {
          type: 'TailRecursion',
          name: '尾递归优化',
          category: NodeCategory.LOGIC,
          description: '实现尾递归优化',
          tags: ['尾递归', '优化', '性能'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup([...functionNodes, ...moreFunctionNodes], '函数和方法节点');
  }

  /**
   * 注册协程和异步节点 (081-100)
   */
  private registerCoroutineAsyncNodes(): void {
    const asyncNodes = [
      {
        nodeClass: CoroutineCreateNode,
        definition: {
          type: 'CoroutineCreate',
          name: '协程创建',
          category: NodeCategory.LOGIC,
          description: '创建协程',
          tags: ['协程', '生成器', '异步'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoroutineStartNode,
        definition: {
          type: 'CoroutineStart',
          name: '协程启动',
          category: NodeCategory.LOGIC,
          description: '启动协程',
          tags: ['协程', '启动', '执行'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoroutineYieldNode,
        definition: {
          type: 'CoroutineYield',
          name: '协程暂停',
          category: NodeCategory.LOGIC,
          description: '协程让出执行权',
          tags: ['协程', '暂停', 'yield'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoroutineResumeNode,
        definition: {
          type: 'CoroutineResume',
          name: '协程恢复',
          category: NodeCategory.LOGIC,
          description: '恢复协程执行',
          tags: ['协程', '恢复', '继续'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoroutineWaitNode,
        definition: {
          type: 'CoroutineWait',
          name: '协程等待',
          category: NodeCategory.LOGIC,
          description: '等待协程完成',
          tags: ['协程', '等待', '同步'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: CoroutineCancelNode,
        definition: {
          type: 'CoroutineCancel',
          name: '协程取消',
          category: NodeCategory.LOGIC,
          description: '取消协程执行',
          tags: ['协程', '取消', '终止'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: AsyncFunctionNode,
        definition: {
          type: 'AsyncFunction',
          name: '异步函数',
          category: NodeCategory.LOGIC,
          description: '异步函数定义和执行',
          tags: ['异步', '函数', 'async'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: AwaitNode,
        definition: {
          type: 'Await',
          name: '等待异步',
          category: NodeCategory.LOGIC,
          description: '等待异步操作完成',
          tags: ['异步', '等待', 'await'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PromiseCreateNode,
        definition: {
          type: 'PromiseCreate',
          name: 'Promise创建',
          category: NodeCategory.LOGIC,
          description: '创建Promise',
          tags: ['Promise', '创建', '异步'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PromiseResolveNode,
        definition: {
          type: 'PromiseResolve',
          name: 'Promise解决',
          category: NodeCategory.LOGIC,
          description: '解决Promise',
          tags: ['Promise', '解决', 'resolve'],
          version: '1.0.0'
        }
      }
    ];

    // 添加更多Promise和异步节点
    const moreAsyncNodes = [
      {
        nodeClass: PromiseRejectNode,
        definition: {
          type: 'PromiseReject',
          name: 'Promise拒绝',
          category: NodeCategory.LOGIC,
          description: '拒绝Promise',
          tags: ['Promise', '拒绝', 'reject'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PromiseChainNode,
        definition: {
          type: 'PromiseChain',
          name: 'Promise链式',
          category: NodeCategory.LOGIC,
          description: 'Promise链式调用',
          tags: ['Promise', '链式', 'then'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PromiseAllNode,
        definition: {
          type: 'PromiseAll',
          name: 'Promise并行',
          category: NodeCategory.LOGIC,
          description: '并行执行多个Promise',
          tags: ['Promise', '并行', 'all'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: PromiseRaceNode,
        definition: {
          type: 'PromiseRace',
          name: 'Promise竞争',
          category: NodeCategory.LOGIC,
          description: 'Promise竞争执行',
          tags: ['Promise', '竞争', 'race'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: AsyncIteratorNode,
        definition: {
          type: 'AsyncIterator',
          name: '异步迭代器',
          category: NodeCategory.LOGIC,
          description: '异步迭代器',
          tags: ['异步', '迭代器', '遍历'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: AsyncGeneratorNode,
        definition: {
          type: 'AsyncGenerator',
          name: '异步生成器',
          category: NodeCategory.LOGIC,
          description: '异步生成器',
          tags: ['异步', '生成器', '流'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: TaskQueueNode,
        definition: {
          type: 'TaskQueue',
          name: '任务队列',
          category: NodeCategory.LOGIC,
          description: '任务队列管理',
          tags: ['任务', '队列', '调度'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: EventLoopNode,
        definition: {
          type: 'EventLoop',
          name: '事件循环',
          category: NodeCategory.LOGIC,
          description: '事件循环控制',
          tags: ['事件', '循环', '异步'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MicrotaskNode,
        definition: {
          type: 'Microtask',
          name: '微任务',
          category: NodeCategory.LOGIC,
          description: '微任务调度',
          tags: ['微任务', '调度', '优先级'],
          version: '1.0.0'
        }
      },
      {
        nodeClass: MacrotaskNode,
        definition: {
          type: 'Macrotask',
          name: '宏任务',
          category: NodeCategory.LOGIC,
          description: '宏任务调度',
          tags: ['宏任务', '定时器', '延迟'],
          version: '1.0.0'
        }
      }
    ];

    this.registerNodeGroup([...asyncNodes, ...moreAsyncNodes], '协程和异步节点');
  }

  /**
   * 注册节点组
   */
  private registerNodeGroup(nodes: any[], groupName: string): void {
    console.log(`注册${groupName}...`);
    
    for (const { nodeClass, definition } of nodes) {
      try {
        this.registry.register({
          ...definition,
          factory: () => new nodeClass()
        });
        
        this.registeredNodes.add(definition.type);
        console.log(`✓ 已注册: ${definition.name} (${definition.type})`);
      } catch (error) {
        console.error(`✗ 注册失败: ${definition.name}`, error);
      }
    }
  }

  /**
   * 获取已注册的节点列表
   */
  public getRegisteredNodes(): string[] {
    return Array.from(this.registeredNodes);
  }

  /**
   * 检查节点是否已注册
   */
  public isNodeRegistered(nodeType: string): boolean {
    return this.registeredNodes.has(nodeType);
  }
}
