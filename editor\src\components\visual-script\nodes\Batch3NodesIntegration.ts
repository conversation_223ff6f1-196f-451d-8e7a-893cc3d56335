/**
 * 第三批次节点编辑器集成 (101-150)
 * 将核心引擎基础节点集成到可视化编辑器中
 */

import { NodePalette } from '../NodePalette';
import { NodeCategory } from '../../../engine/src/visual-script-v2/core/types';
import { Batch3NodesRegistry } from '../../../engine/src/visual-script-v2/nodes/registry/Batch3NodesRegistry';

/**
 * 第三批次节点编辑器集成类
 */
export class Batch3NodesIntegration {
  private nodePalette: NodePalette;
  private registry: Batch3NodesRegistry;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodePalette: NodePalette) {
    this.nodePalette = nodePalette;
    this.registry = Batch3NodesRegistry.getInstance();
  }

  /**
   * 集成所有第三批次节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成第三批次节点到编辑器...');

    // 注册节点到引擎
    this.registry.registerAllNodes();

    // 集成系统管理节点
    this.integrateSystemManagementNodes();

    // 集成实体组件节点
    this.integrateEntityComponentNodes();

    // 集成基础变换节点
    this.integrateTransformNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('第三批次节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成系统管理节点 (101-120)
   */
  private integrateSystemManagementNodes(): void {
    const systemNodes = [
      {
        type: 'EngineInitialize',
        name: '引擎初始化',
        description: '初始化引擎系统',
        icon: '🚀',
        color: '#2196f3',
        inputs: ['input', 'config', 'plugins', 'debug'],
        outputs: ['output', 'engine', 'initialized', 'error']
      },
      {
        type: 'WorldCreate',
        name: '世界创建',
        description: '创建游戏世界实例',
        icon: '🌍',
        color: '#4caf50',
        inputs: ['input', 'engine', 'worldName', 'worldConfig'],
        outputs: ['output', 'world', 'created']
      },
      {
        type: 'SceneManager',
        name: '场景管理',
        description: '场景加载和切换管理',
        icon: '🎬',
        color: '#ff9800',
        inputs: ['input', 'world', 'sceneName', 'sceneData', 'operation'],
        outputs: ['output', 'scene', 'loaded', 'unloaded', 'switched']
      },
      {
        type: 'SystemRegister',
        name: '系统注册',
        description: '注册和管理系统组件',
        icon: '⚙️',
        color: '#9c27b0',
        inputs: ['input', 'engine', 'systemName', 'systemClass', 'systemConfig', 'priority'],
        outputs: ['output', 'system', 'registered']
      },
      {
        type: 'TimeManager',
        name: '时间管理',
        description: '游戏时间和帧率控制',
        icon: '⏰',
        color: '#607d8b',
        inputs: ['input', 'engine', 'timeScale', 'targetFrameRate', 'maxDeltaTime'],
        outputs: ['output', 'deltaTime', 'totalTime', 'frameRate', 'updated']
      },
      {
        type: 'EventSystem',
        name: '事件系统',
        description: '事件发布和订阅管理',
        icon: '📡',
        color: '#e91e63',
        inputs: ['input', 'engine', 'eventType', 'eventData', 'listener', 'operation'],
        outputs: ['output', 'eventEmitted', 'listenerAdded', 'listenerRemoved']
      },
      {
        type: 'MemoryManager',
        name: '内存管理',
        description: '内存分配和垃圾回收管理',
        icon: '🧠',
        color: '#795548',
        inputs: ['input', 'engine', 'operation', 'objectType', 'poolSize', 'gcThreshold'],
        outputs: ['output', 'object', 'memoryInfo', 'poolCreated', 'gcTriggered']
      }
    ];

    this.integrateNodeGroup(systemNodes, '系统管理', '#2196f3');
  }

  /**
   * 集成实体组件节点 (121-140)
   */
  private integrateEntityComponentNodes(): void {
    const entityNodes = [
      {
        type: 'EntityCreate',
        name: '实体创建',
        description: '创建新的实体',
        icon: '🎯',
        color: '#4caf50',
        inputs: ['input', 'world', 'entityName', 'components', 'parent'],
        outputs: ['output', 'entity', 'created']
      },
      {
        type: 'EntityDestroy',
        name: '实体销毁',
        description: '销毁实体及其组件',
        icon: '💥',
        color: '#f44336',
        inputs: ['input', 'world', 'entity', 'destroyChildren'],
        outputs: ['output', 'destroyed']
      },
      {
        type: 'ComponentAdd',
        name: '组件添加',
        description: '向实体添加组件',
        icon: '➕',
        color: '#8bc34a',
        inputs: ['input', 'entity', 'componentType', 'componentData', 'componentClass'],
        outputs: ['output', 'component', 'added']
      },
      {
        type: 'ComponentRemove',
        name: '组件移除',
        description: '从实体移除组件',
        icon: '➖',
        color: '#ff5722',
        inputs: ['input', 'entity', 'componentType'],
        outputs: ['output', 'component', 'removed']
      },
      {
        type: 'ComponentGet',
        name: '组件获取',
        description: '获取实体的组件',
        icon: '🔍',
        color: '#03a9f4',
        inputs: ['input', 'entity', 'componentType'],
        outputs: ['output', 'component', 'found', 'notFound']
      },
      {
        type: 'EntityQuery',
        name: '实体查询',
        description: '根据组件类型查询实体',
        icon: '🔎',
        color: '#673ab7',
        inputs: ['input', 'world', 'componentTypes', 'queryType', 'filter'],
        outputs: ['output', 'entities', 'count']
      },
      {
        type: 'EntityHierarchy',
        name: '实体层级',
        description: '管理实体的父子关系',
        icon: '🌳',
        color: '#009688',
        inputs: ['input', 'entity', 'parent', 'operation'],
        outputs: ['output', 'parent', 'children', 'attached', 'detached']
      }
    ];

    this.integrateNodeGroup(entityNodes, '实体组件', '#4caf50');
  }

  /**
   * 集成基础变换节点 (141-150)
   */
  private integrateTransformNodes(): void {
    const transformNodes = [
      {
        type: 'PositionSet',
        name: '位置设置',
        description: '设置实体的位置',
        icon: '📍',
        color: '#ff9800',
        inputs: ['input', 'entity', 'x', 'y', 'z', 'position', 'space'],
        outputs: ['output', 'newPosition', 'updated']
      },
      {
        type: 'RotationSet',
        name: '旋转设置',
        description: '设置实体的旋转',
        icon: '🔄',
        color: '#3f51b5',
        inputs: ['input', 'entity', 'x', 'y', 'z', 'quaternion', 'angleMode', 'space'],
        outputs: ['output', 'newRotation', 'updated']
      },
      {
        type: 'ScaleSet',
        name: '缩放设置',
        description: '设置实体的缩放',
        icon: '📏',
        color: '#9c27b0',
        inputs: ['input', 'entity', 'x', 'y', 'z', 'scale', 'uniform'],
        outputs: ['output', 'newScale', 'updated']
      },
      {
        type: 'TransformMatrix',
        name: '变换矩阵',
        description: '计算和应用变换矩阵',
        icon: '🔢',
        color: '#607d8b',
        inputs: ['input', 'entity', 'matrix', 'operation'],
        outputs: ['output', 'matrix', 'position', 'rotation', 'scale']
      },
      {
        type: 'CoordinateConvert',
        name: '坐标转换',
        description: '世界坐标和本地坐标转换',
        icon: '🗺️',
        color: '#795548',
        inputs: ['input', 'entity', 'point', 'fromSpace', 'toSpace'],
        outputs: ['output', 'convertedPoint']
      }
    ];

    this.integrateNodeGroup(transformNodes, '基础变换', '#ff9800');
  }

  /**
   * 集成节点组
   */
  private integrateNodeGroup(nodes: any[], categoryName: string, categoryColor: string): void {
    const nodeTypes: string[] = [];

    for (const node of nodes) {
      try {
        // 添加到节点面板
        this.nodePalette.addNode({
          type: node.type,
          name: node.name,
          description: node.description,
          category: categoryName,
          icon: node.icon,
          color: node.color,
          inputs: node.inputs,
          outputs: node.outputs
        });

        this.registeredNodes.add(node.type);
        nodeTypes.push(node.type);

        console.log(`✓ 已集成节点: ${node.name} (${node.type})`);
      } catch (error) {
        console.error(`✗ 节点集成失败: ${node.name}`, error);
      }
    }

    this.categoryNodes.set(categoryName, nodeTypes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 添加节点分类
    this.nodePalette.addCategory({
      name: '系统管理',
      icon: '🚀',
      color: '#2196f3',
      description: '引擎系统管理和核心功能节点'
    });

    this.nodePalette.addCategory({
      name: '实体组件',
      icon: '🎯',
      color: '#4caf50',
      description: '实体组件系统(ECS)相关节点'
    });

    this.nodePalette.addCategory({
      name: '基础变换',
      icon: '📍',
      color: '#ff9800',
      description: '位置、旋转、缩放等基础变换节点'
    });
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 设置搜索标签
    const searchTags = new Map([
      ['系统管理', ['引擎', '系统', '世界', '场景', '时间', '事件', '内存']],
      ['实体组件', ['实体', '组件', 'ECS', '查询', '层级']],
      ['基础变换', ['位置', '旋转', '缩放', '矩阵', '坐标', '变换']]
    ]);

    for (const [category, tags] of searchTags) {
      this.nodePalette.addSearchTags(category, tags);
    }
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): {
    totalNodes: number;
    categories: number;
    nodesByCategory: Map<string, string[]>;
  } {
    return {
      totalNodes: this.registeredNodes.size,
      categories: this.categoryNodes.size,
      nodesByCategory: this.categoryNodes
    };
  }
}
