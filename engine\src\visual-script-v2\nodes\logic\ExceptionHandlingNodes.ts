/**
 * 异常处理节点 (051-060)
 * 实现错误边界、断路器、超时处理等高级异常处理功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 051 - 错误边界节点
 * 提供错误边界保护，防止错误传播
 */
export class ErrorBoundaryNode extends BaseNode {
  constructor() {
    super('ErrorBoundary', '错误边界', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('fallback', '回退值', DataType.ANY);
    this.addInputPort('errorHandler', '错误处理器', DataType.FUNCTION);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('success', '成功', DataType.BOOLEAN);
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      // 执行输入逻辑
      await this.executeConnectedNodes(context, 'input');
      
      // 设置成功状态
      this.setOutputValue('success', true);
      this.setOutputValue('error', null);
      
      // 触发输出
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.warn(`错误边界捕获异常: ${error.message}`);
      
      // 设置错误状态
      this.setOutputValue('success', false);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      // 执行错误处理器
      const errorHandler = this.getInputValue('errorHandler');
      if (errorHandler && typeof errorHandler === 'function') {
        try {
          await errorHandler(error, context);
        } catch (handlerError) {
          console.error('错误处理器执行失败:', handlerError);
        }
      }
      
      // 使用回退值
      const fallback = this.getInputValue('fallback');
      if (fallback !== undefined) {
        this.setOutputValue('output', fallback);
      }
    }
  }
}

/**
 * 052 - 断路器节点
 * 实现断路器模式，防止级联故障
 */
export class CircuitBreakerNode extends BaseNode {
  private failureCount = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor() {
    super('CircuitBreaker', '断路器', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('failureThreshold', '失败阈值', DataType.NUMBER);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);
    this.addInputPort('resetTimeout', '重置超时', DataType.NUMBER);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('rejected', '被拒绝', DataType.TRIGGER);
    this.addOutputPort('state', '状态', DataType.STRING);
    this.addOutputPort('failureCount', '失败次数', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const failureThreshold = this.getInputValue('failureThreshold') || 5;
    const timeout = this.getInputValue('timeout') || 5000;
    const resetTimeout = this.getInputValue('resetTimeout') || 60000;
    
    const now = Date.now();
    
    // 检查是否需要从OPEN状态转换到HALF_OPEN
    if (this.state === 'OPEN' && now - this.lastFailureTime > resetTimeout) {
      this.state = 'HALF_OPEN';
      this.failureCount = 0;
    }
    
    // 输出当前状态
    this.setOutputValue('state', this.state);
    this.setOutputValue('failureCount', this.failureCount);
    
    // 如果断路器是开启状态，直接拒绝
    if (this.state === 'OPEN') {
      console.warn('断路器开启，请求被拒绝');
      await this.triggerOutput(context, 'rejected');
      return;
    }
    
    try {
      // 执行带超时的操作
      await Promise.race([
        this.executeConnectedNodes(context, 'input'),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('操作超时')), timeout)
        )
      ]);
      
      // 成功执行，重置失败计数
      if (this.state === 'HALF_OPEN') {
        this.state = 'CLOSED';
      }
      this.failureCount = 0;
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      this.failureCount++;
      this.lastFailureTime = now;
      
      // 检查是否达到失败阈值
      if (this.failureCount >= failureThreshold) {
        this.state = 'OPEN';
        console.warn(`断路器开启，失败次数: ${this.failureCount}`);
      }
      
      await this.triggerOutput(context, 'rejected');
      throw error;
    }
  }
}

/**
 * 053 - 超时处理节点
 * 处理操作超时异常
 */
export class TimeoutHandlerNode extends BaseNode {
  constructor() {
    super('TimeoutHandler', '超时处理', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);
    this.addInputPort('onTimeout', '超时回调', DataType.FUNCTION);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('timeout', '超时', DataType.TRIGGER);
    this.addOutputPort('duration', '执行时间', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const timeout = this.getInputValue('timeout') || 5000;
    const onTimeout = this.getInputValue('onTimeout');
    
    const startTime = Date.now();
    
    try {
      await Promise.race([
        this.executeConnectedNodes(context, 'input'),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('操作超时')), timeout)
        )
      ]);
      
      const duration = Date.now() - startTime;
      this.setOutputValue('duration', duration);
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.setOutputValue('duration', duration);
      
      if (error.message === '操作超时') {
        console.warn(`操作超时: ${timeout}ms`);
        
        // 执行超时回调
        if (onTimeout && typeof onTimeout === 'function') {
          try {
            await onTimeout(timeout, duration, context);
          } catch (callbackError) {
            console.error('超时回调执行失败:', callbackError);
          }
        }
        
        await this.triggerOutput(context, 'timeout');
      } else {
        throw error;
      }
    }
  }
}

/**
 * 054 - 资源清理节点
 * 自动管理资源的创建和清理
 */
export class ResourceCleanupNode extends BaseNode {
  private resources: Map<string, any> = new Map();
  private cleanupHandlers: Map<string, Function> = new Map();
  
  constructor() {
    super('ResourceCleanup', '资源清理', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('resource', '资源', DataType.ANY);
    this.addInputPort('resourceId', '资源ID', DataType.STRING);
    this.addInputPort('cleanupHandler', '清理处理器', DataType.FUNCTION);
    this.addInputPort('autoCleanup', '自动清理', DataType.BOOLEAN);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('resource', '资源', DataType.ANY);
    this.addOutputPort('cleaned', '已清理', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const resource = this.getInputValue('resource');
    const resourceId = this.getInputValue('resourceId') || `resource_${Date.now()}`;
    const cleanupHandler = this.getInputValue('cleanupHandler');
    const autoCleanup = this.getInputValue('autoCleanup') !== false;
    
    try {
      // 注册资源
      if (resource !== undefined) {
        this.resources.set(resourceId, resource);
        
        if (cleanupHandler && typeof cleanupHandler === 'function') {
          this.cleanupHandlers.set(resourceId, cleanupHandler);
        }
        
        this.setOutputValue('resource', resource);
      }
      
      // 执行主逻辑
      await this.executeConnectedNodes(context, 'input');
      await this.triggerOutput(context, 'output');
      
    } finally {
      // 自动清理资源
      if (autoCleanup) {
        await this.cleanupResource(resourceId);
      }
    }
  }
  
  private async cleanupResource(resourceId: string): Promise<void> {
    const resource = this.resources.get(resourceId);
    const cleanupHandler = this.cleanupHandlers.get(resourceId);
    
    if (resource && cleanupHandler) {
      try {
        await cleanupHandler(resource);
        console.log(`资源已清理: ${resourceId}`);
      } catch (error) {
        console.error(`资源清理失败: ${resourceId}`, error);
      }
    }
    
    this.resources.delete(resourceId);
    this.cleanupHandlers.delete(resourceId);
    
    await this.triggerOutput(null as any, 'cleaned');
  }
  
  // 手动清理所有资源
  async dispose(): Promise<void> {
    for (const resourceId of this.resources.keys()) {
      await this.cleanupResource(resourceId);
    }
    super.dispose();
  }
}

/**
 * 055 - 内存泄漏检测节点
 * 监控内存使用情况，检测潜在的内存泄漏
 */
export class MemoryLeakDetectionNode extends BaseNode {
  private memorySnapshots: number[] = [];
  private maxSnapshots = 10;

  constructor() {
    super('MemoryLeakDetection', '内存泄漏检测', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('threshold', '阈值', DataType.NUMBER);
    this.addInputPort('sampleInterval', '采样间隔', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('memoryUsage', '内存使用', DataType.NUMBER);
    this.addOutputPort('leakDetected', '检测到泄漏', DataType.TRIGGER);
    this.addOutputPort('trend', '趋势', DataType.STRING);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const threshold = this.getInputValue('threshold') || 100; // MB
    const sampleInterval = this.getInputValue('sampleInterval') || 1000; // ms

    // 获取当前内存使用情况
    const memoryUsage = this.getMemoryUsage();
    this.setOutputValue('memoryUsage', memoryUsage);

    // 记录内存快照
    this.memorySnapshots.push(memoryUsage);
    if (this.memorySnapshots.length > this.maxSnapshots) {
      this.memorySnapshots.shift();
    }

    // 分析内存趋势
    const trend = this.analyzeMemoryTrend();
    this.setOutputValue('trend', trend);

    // 检测内存泄漏
    if (memoryUsage > threshold || trend === 'INCREASING') {
      console.warn(`检测到潜在内存泄漏: ${memoryUsage}MB, 趋势: ${trend}`);
      await this.triggerOutput(context, 'leakDetected');
    }

    await this.executeConnectedNodes(context, 'input');
    await this.triggerOutput(context, 'output');
  }

  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      // Node.js环境
      const usage = process.memoryUsage();
      return Math.round(usage.heapUsed / 1024 / 1024); // MB
    } else if (typeof performance !== 'undefined' && (performance as any).memory) {
      // 浏览器环境
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
    }
    return 0;
  }

  private analyzeMemoryTrend(): string {
    if (this.memorySnapshots.length < 3) {
      return 'INSUFFICIENT_DATA';
    }

    const recent = this.memorySnapshots.slice(-3);
    const isIncreasing = recent.every((val, i) => i === 0 || val > recent[i - 1]);
    const isDecreasing = recent.every((val, i) => i === 0 || val < recent[i - 1]);

    if (isIncreasing) return 'INCREASING';
    if (isDecreasing) return 'DECREASING';
    return 'STABLE';
  }
}

/**
 * 056 - 死锁检测节点
 * 检测和处理死锁情况
 */
export class DeadlockDetectionNode extends BaseNode {
  private static lockRegistry = new Map<string, { owner: string; waiters: string[] }>();
  private lockId: string;

  constructor() {
    super('DeadlockDetection', '死锁检测', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('lockId', '锁ID', DataType.STRING);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('deadlockDetected', '检测到死锁', DataType.TRIGGER);
    this.addOutputPort('lockAcquired', '获得锁', DataType.TRIGGER);
    this.addOutputPort('lockReleased', '释放锁', DataType.TRIGGER);

    this.lockId = `lock_${this.id}`;
  }

  async execute(context: IExecutionContext): Promise<void> {
    const lockId = this.getInputValue('lockId') || this.lockId;
    const timeout = this.getInputValue('timeout') || 5000;

    try {
      // 尝试获取锁
      const acquired = await this.acquireLock(lockId, timeout);

      if (acquired) {
        await this.triggerOutput(context, 'lockAcquired');

        try {
          // 执行受保护的代码
          await this.executeConnectedNodes(context, 'input');
          await this.triggerOutput(context, 'output');
        } finally {
          // 释放锁
          this.releaseLock(lockId);
          await this.triggerOutput(context, 'lockReleased');
        }
      } else {
        console.warn(`检测到死锁或超时: ${lockId}`);
        await this.triggerOutput(context, 'deadlockDetected');
      }
    } catch (error) {
      this.releaseLock(lockId);
      throw error;
    }
  }

  private async acquireLock(lockId: string, timeout: number): Promise<boolean> {
    const nodeId = this.id;
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const lock = DeadlockDetectionNode.lockRegistry.get(lockId);

      if (!lock) {
        // 锁不存在，创建并获取
        DeadlockDetectionNode.lockRegistry.set(lockId, {
          owner: nodeId,
          waiters: []
        });
        return true;
      }

      if (lock.owner === nodeId) {
        // 已经拥有锁
        return true;
      }

      // 检测死锁
      if (this.detectDeadlock(lockId, nodeId)) {
        console.warn(`检测到死锁: ${lockId} -> ${nodeId}`);
        return false;
      }

      // 加入等待队列
      if (!lock.waiters.includes(nodeId)) {
        lock.waiters.push(nodeId);
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return false;
  }

  private releaseLock(lockId: string): void {
    const lock = DeadlockDetectionNode.lockRegistry.get(lockId);
    if (lock && lock.owner === this.id) {
      if (lock.waiters.length > 0) {
        // 将锁转移给下一个等待者
        lock.owner = lock.waiters.shift()!;
      } else {
        // 删除锁
        DeadlockDetectionNode.lockRegistry.delete(lockId);
      }
    }
  }

  private detectDeadlock(lockId: string, nodeId: string): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (currentLock: string): boolean => {
      if (recursionStack.has(currentLock)) {
        return true; // 发现环，即死锁
      }

      if (visited.has(currentLock)) {
        return false;
      }

      visited.add(currentLock);
      recursionStack.add(currentLock);

      const lock = DeadlockDetectionNode.lockRegistry.get(currentLock);
      if (lock) {
        // 检查等待者是否持有其他锁
        for (const waiter of lock.waiters) {
          for (const [otherLockId, otherLock] of DeadlockDetectionNode.lockRegistry) {
            if (otherLock.owner === waiter && dfs(otherLockId)) {
              return true;
            }
          }
        }
      }

      recursionStack.delete(currentLock);
      return false;
    };

    return dfs(lockId);
  }
}

/**
 * 057 - 性能监控节点
 * 监控系统性能指标，检测性能异常
 */
export class PerformanceMonitorNode extends BaseNode {
  private performanceData: Array<{
    timestamp: number;
    cpuUsage: number;
    memoryUsage: number;
    executionTime: number;
  }> = [];

  constructor() {
    super('PerformanceMonitor', '性能监控', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('cpuThreshold', 'CPU阈值', DataType.NUMBER);
    this.addInputPort('memoryThreshold', '内存阈值', DataType.NUMBER);
    this.addInputPort('timeThreshold', '时间阈值', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('performanceAlert', '性能警报', DataType.TRIGGER);
    this.addOutputPort('metrics', '指标', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const cpuThreshold = this.getInputValue('cpuThreshold') || 80; // %
    const memoryThreshold = this.getInputValue('memoryThreshold') || 80; // %
    const timeThreshold = this.getInputValue('timeThreshold') || 1000; // ms

    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();

    try {
      // 执行被监控的操作
      await this.executeConnectedNodes(context, 'input');

      const endTime = performance.now();
      const endMemory = this.getMemoryUsage();
      const executionTime = endTime - startTime;

      // 计算性能指标
      const metrics = {
        cpuUsage: this.getCpuUsage(),
        memoryUsage: endMemory,
        memoryDelta: endMemory - startMemory,
        executionTime,
        timestamp: Date.now()
      };

      // 记录性能数据
      this.performanceData.push(metrics);
      if (this.performanceData.length > 100) {
        this.performanceData.shift();
      }

      this.setOutputValue('metrics', metrics);

      // 检查性能阈值
      if (metrics.cpuUsage > cpuThreshold ||
          metrics.memoryUsage > memoryThreshold ||
          metrics.executionTime > timeThreshold) {

        console.warn('性能警报:', metrics);
        await this.triggerOutput(context, 'performanceAlert');
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      const endTime = performance.now();
      const executionTime = endTime - startTime;

      console.error(`性能监控中发生错误，执行时间: ${executionTime}ms`, error);
      throw error;
    }
  }

  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return Math.round((usage.heapUsed / usage.heapTotal) * 100);
    } else if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100);
    }
    return 0;
  }

  private getCpuUsage(): number {
    // 简化的CPU使用率计算
    // 在实际应用中，可能需要更复杂的计算方法
    if (typeof process !== 'undefined' && process.cpuUsage) {
      const usage = process.cpuUsage();
      return Math.round((usage.user + usage.system) / 1000000); // 转换为毫秒
    }
    return 0;
  }
}

/**
 * 058 - 健康检查节点
 * 执行系统健康检查，确保系统正常运行
 */
export class HealthCheckNode extends BaseNode {
  private healthChecks: Map<string, Function> = new Map();

  constructor() {
    super('HealthCheck', '健康检查', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('checks', '检查项', DataType.ARRAY);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('healthy', '健康', DataType.TRIGGER);
    this.addOutputPort('unhealthy', '不健康', DataType.TRIGGER);
    this.addOutputPort('results', '检查结果', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const checks = this.getInputValue('checks') || [];
    const timeout = this.getInputValue('timeout') || 5000;

    const results: any = {
      overall: 'healthy',
      checks: {},
      timestamp: Date.now()
    };

    try {
      // 执行所有健康检查
      const checkPromises = checks.map(async (check: any) => {
        const checkName = check.name || 'unknown';
        const checkFunction = check.function;

        try {
          const startTime = Date.now();
          const result = await Promise.race([
            checkFunction(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('健康检查超时')), timeout)
            )
          ]);

          const duration = Date.now() - startTime;

          results.checks[checkName] = {
            status: 'healthy',
            result,
            duration,
            error: null
          };

        } catch (error) {
          results.checks[checkName] = {
            status: 'unhealthy',
            result: null,
            duration: Date.now() - Date.now(),
            error: error.message
          };

          results.overall = 'unhealthy';
        }
      });

      await Promise.all(checkPromises);

      // 添加基本系统检查
      await this.performBasicChecks(results);

      this.setOutputValue('results', results);

      if (results.overall === 'healthy') {
        console.log('系统健康检查通过');
        await this.triggerOutput(context, 'healthy');
      } else {
        console.warn('系统健康检查失败:', results);
        await this.triggerOutput(context, 'unhealthy');
      }

      await this.executeConnectedNodes(context, 'input');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      results.overall = 'unhealthy';
      results.error = error.message;
      this.setOutputValue('results', results);

      await this.triggerOutput(context, 'unhealthy');
      throw error;
    }
  }

  private async performBasicChecks(results: any): Promise<void> {
    // 内存检查
    const memoryUsage = this.getMemoryUsage();
    results.checks['memory'] = {
      status: memoryUsage < 90 ? 'healthy' : 'unhealthy',
      result: { usage: memoryUsage },
      duration: 0,
      error: null
    };

    if (memoryUsage >= 90) {
      results.overall = 'unhealthy';
    }

    // 响应时间检查
    const startTime = Date.now();
    await new Promise(resolve => setTimeout(resolve, 1));
    const responseTime = Date.now() - startTime;

    results.checks['responseTime'] = {
      status: responseTime < 100 ? 'healthy' : 'unhealthy',
      result: { responseTime },
      duration: responseTime,
      error: null
    };

    if (responseTime >= 100) {
      results.overall = 'unhealthy';
    }
  }

  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return Math.round((usage.heapUsed / usage.heapTotal) * 100);
    } else if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100);
    }
    return 0;
  }
}

/**
 * 059 - 故障转移节点
 * 实现故障转移机制，在主服务失败时切换到备用服务
 */
export class FailoverNode extends BaseNode {
  private currentServiceIndex = 0;
  private failedServices = new Set<number>();

  constructor() {
    super('Failover', '故障转移', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('services', '服务列表', DataType.ARRAY);
    this.addInputPort('retryAttempts', '重试次数', DataType.NUMBER);
    this.addInputPort('retryDelay', '重试延迟', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('failover', '故障转移', DataType.TRIGGER);
    this.addOutputPort('allFailed', '全部失败', DataType.TRIGGER);
    this.addOutputPort('currentService', '当前服务', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const services = this.getInputValue('services') || [];
    const retryAttempts = this.getInputValue('retryAttempts') || 3;
    const retryDelay = this.getInputValue('retryDelay') || 1000;

    if (services.length === 0) {
      throw new Error('没有可用的服务');
    }

    let lastError: Error | null = null;

    // 尝试所有可用服务
    for (let attempt = 0; attempt < services.length; attempt++) {
      const serviceIndex = (this.currentServiceIndex + attempt) % services.length;

      // 跳过已知失败的服务
      if (this.failedServices.has(serviceIndex)) {
        continue;
      }

      const service = services[serviceIndex];
      this.setOutputValue('currentService', service);

      try {
        // 尝试执行服务
        await this.executeService(service, context);

        // 成功执行，更新当前服务索引
        this.currentServiceIndex = serviceIndex;
        this.failedServices.delete(serviceIndex);

        await this.triggerOutput(context, 'output');
        return;

      } catch (error) {
        lastError = error;
        console.warn(`服务 ${serviceIndex} 执行失败:`, error.message);

        // 标记服务为失败
        this.failedServices.add(serviceIndex);

        // 如果不是最后一个服务，触发故障转移
        if (attempt < services.length - 1) {
          console.log(`故障转移到服务 ${(serviceIndex + 1) % services.length}`);
          await this.triggerOutput(context, 'failover');

          // 等待重试延迟
          if (retryDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          }
        }
      }
    }

    // 所有服务都失败了
    console.error('所有服务都失败了');
    await this.triggerOutput(context, 'allFailed');

    if (lastError) {
      throw lastError;
    } else {
      throw new Error('所有服务都不可用');
    }
  }

  private async executeService(service: any, context: IExecutionContext): Promise<void> {
    if (typeof service === 'function') {
      await service(context);
    } else if (service && typeof service.execute === 'function') {
      await service.execute(context);
    } else {
      // 假设服务是一个节点连接
      await this.executeConnectedNodes(context, 'input');
    }
  }

  // 重置失败状态
  public resetFailures(): void {
    this.failedServices.clear();
    this.currentServiceIndex = 0;
  }
}

/**
 * 060 - 灾难恢复节点
 * 实现灾难恢复机制，在系统发生严重故障时进行恢复
 */
export class DisasterRecoveryNode extends BaseNode {
  private backupData: Map<string, any> = new Map();
  private recoveryProcedures: Map<string, Function> = new Map();

  constructor() {
    super('DisasterRecovery', '灾难恢复', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('backupData', '备份数据', DataType.OBJECT);
    this.addInputPort('recoveryProcedure', '恢复程序', DataType.FUNCTION);
    this.addInputPort('disasterType', '灾难类型', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('recoveryStarted', '恢复开始', DataType.TRIGGER);
    this.addOutputPort('recoveryCompleted', '恢复完成', DataType.TRIGGER);
    this.addOutputPort('recoveryFailed', '恢复失败', DataType.TRIGGER);
    this.addOutputPort('recoveryStatus', '恢复状态', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const backupData = this.getInputValue('backupData');
    const recoveryProcedure = this.getInputValue('recoveryProcedure');
    const disasterType = this.getInputValue('disasterType') || 'unknown';

    const recoveryStatus = {
      type: disasterType,
      startTime: Date.now(),
      endTime: null,
      success: false,
      steps: [],
      error: null
    };

    try {
      console.log(`开始灾难恢复: ${disasterType}`);
      await this.triggerOutput(context, 'recoveryStarted');

      // 步骤1: 备份当前状态
      recoveryStatus.steps.push('备份当前状态');
      await this.backupCurrentState();

      // 步骤2: 停止相关服务
      recoveryStatus.steps.push('停止相关服务');
      await this.stopServices();

      // 步骤3: 恢复数据
      if (backupData) {
        recoveryStatus.steps.push('恢复数据');
        await this.restoreData(backupData);
      }

      // 步骤4: 执行自定义恢复程序
      if (recoveryProcedure && typeof recoveryProcedure === 'function') {
        recoveryStatus.steps.push('执行自定义恢复程序');
        await recoveryProcedure(context, recoveryStatus);
      }

      // 步骤5: 重启服务
      recoveryStatus.steps.push('重启服务');
      await this.restartServices();

      // 步骤6: 验证恢复
      recoveryStatus.steps.push('验证恢复');
      await this.verifyRecovery();

      recoveryStatus.success = true;
      recoveryStatus.endTime = Date.now();

      console.log(`灾难恢复完成: ${disasterType}`);
      this.setOutputValue('recoveryStatus', recoveryStatus);

      await this.triggerOutput(context, 'recoveryCompleted');
      await this.executeConnectedNodes(context, 'input');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      recoveryStatus.success = false;
      recoveryStatus.endTime = Date.now();
      recoveryStatus.error = error.message;

      console.error(`灾难恢复失败: ${disasterType}`, error);
      this.setOutputValue('recoveryStatus', recoveryStatus);

      await this.triggerOutput(context, 'recoveryFailed');
      throw error;
    }
  }

  private async backupCurrentState(): Promise<void> {
    // 备份当前系统状态
    const timestamp = Date.now();
    const stateBackup = {
      timestamp,
      // 这里可以添加具体的状态备份逻辑
    };

    this.backupData.set(`state_${timestamp}`, stateBackup);
    console.log('当前状态已备份');
  }

  private async stopServices(): Promise<void> {
    // 停止相关服务
    console.log('正在停止相关服务...');
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private async restoreData(backupData: any): Promise<void> {
    // 恢复数据
    console.log('正在恢复数据...');

    if (backupData && typeof backupData === 'object') {
      for (const [key, value] of Object.entries(backupData)) {
        this.backupData.set(key, value);
      }
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  private async restartServices(): Promise<void> {
    // 重启服务
    console.log('正在重启服务...');
    await new Promise(resolve => setTimeout(resolve, 1500));
  }

  private async verifyRecovery(): Promise<void> {
    // 验证恢复是否成功
    console.log('正在验证恢复...');

    // 这里可以添加具体的验证逻辑
    const isHealthy = Math.random() > 0.1; // 90%的成功率

    if (!isHealthy) {
      throw new Error('恢复验证失败');
    }

    await new Promise(resolve => setTimeout(resolve, 500));
  }
}
