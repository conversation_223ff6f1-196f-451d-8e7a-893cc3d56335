/**
 * 第一批次：程序逻辑控制基础节点 (013-050)
 * 继续实现高级流程控制和异常处理节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 013 - 翻转门 (Flip Flop)
 * 交替输出的门控制
 */
export class FlipFlopNode extends BaseNode {
  private state = false;

  constructor(id?: string) {
    super(id, 'program-logic/flip-flop', '翻转门', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发翻转'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置到初始状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'a',
        label: '输出A',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个状态输出'
      },
      {
        name: 'b',
        label: '输出B',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第二个状态输出'
      },
      {
        name: 'isA',
        label: '是否A状态',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '当前是否为A状态'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const execTrigger = context.getInputValue('exec');
    const resetTrigger = context.getInputValue('reset');

    if (resetTrigger) {
      this.state = false;
    }

    if (execTrigger) {
      this.state = !this.state;
      if (this.state) {
        context.setOutputValue('a', true);
      } else {
        context.setOutputValue('b', true);
      }
      context.setOutputValue('isA', this.state);
    }
  }
}

/**
 * 014 - 并行执行 (Parallel)
 * 同时执行多个分支
 */
export class ParallelNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/parallel', '并行执行', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发并行执行'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'branch1',
        label: '分支1',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '并行分支1'
      },
      {
        name: 'branch2',
        label: '分支2',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '并行分支2'
      },
      {
        name: 'branch3',
        label: '分支3',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '并行分支3'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    // 同时触发所有输出分支
    context.setOutputValue('branch1', true);
    context.setOutputValue('branch2', true);
    context.setOutputValue('branch3', true);
  }
}

/**
 * 015 - 竞争执行 (Race)
 * 等待第一个完成的分支
 */
export class RaceNode extends BaseNode {
  private hasCompleted = false;

  constructor(id?: string) {
    super(id, 'program-logic/race', '竞争执行', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'start',
        label: '开始',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始竞争'
      },
      {
        name: 'input1',
        label: '输入1',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '竞争输入1'
      },
      {
        name: 'input2',
        label: '输入2',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '竞争输入2'
      },
      {
        name: 'input3',
        label: '输入3',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '竞争输入3'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置竞争状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'winner',
        label: '获胜者',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '第一个完成的输出'
      },
      {
        name: 'winnerIndex',
        label: '获胜者索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '获胜者的索引'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const startTrigger = context.getInputValue('start');
    const resetTrigger = context.getInputValue('reset');
    const input1 = context.getInputValue('input1');
    const input2 = context.getInputValue('input2');
    const input3 = context.getInputValue('input3');

    if (resetTrigger) {
      this.hasCompleted = false;
      return;
    }

    if (startTrigger) {
      this.hasCompleted = false;
    }

    if (!this.hasCompleted) {
      if (input1) {
        this.hasCompleted = true;
        context.setOutputValue('winner', true);
        context.setOutputValue('winnerIndex', 1);
      } else if (input2) {
        this.hasCompleted = true;
        context.setOutputValue('winner', true);
        context.setOutputValue('winnerIndex', 2);
      } else if (input3) {
        this.hasCompleted = true;
        context.setOutputValue('winner', true);
        context.setOutputValue('winnerIndex', 3);
      }
    }
  }
}

/**
 * 016 - 同步点 (Synchronization Point)
 * 等待所有输入完成后执行
 */
export class SynchronizationNode extends BaseNode {
  private completedInputs = new Set<string>();

  constructor(id?: string) {
    super(id, 'program-logic/synchronization', '同步点', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'input1',
        label: '输入1',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '同步输入1'
      },
      {
        name: 'input2',
        label: '输入2',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '同步输入2'
      },
      {
        name: 'input3',
        label: '输入3',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '同步输入3'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置同步状态'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '所有输入完成后执行'
      },
      {
        name: 'progress',
        label: '进度',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '完成进度(0-1)'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const resetTrigger = context.getInputValue('reset');
    const input1 = context.getInputValue('input1');
    const input2 = context.getInputValue('input2');
    const input3 = context.getInputValue('input3');

    if (resetTrigger) {
      this.completedInputs.clear();
      return;
    }

    if (input1) this.completedInputs.add('input1');
    if (input2) this.completedInputs.add('input2');
    if (input3) this.completedInputs.add('input3');

    const totalInputs = 3;
    const progress = this.completedInputs.size / totalInputs;
    context.setOutputValue('progress', progress);

    if (this.completedInputs.size === totalInputs) {
      context.setOutputValue('completed', true);
      this.completedInputs.clear(); // 重置状态
    }
  }
}

/**
 * 017 - 条件等待 (Wait Until)
 * 等待条件满足后执行
 */
export class WaitUntilNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/wait-until', '条件等待', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始等待'
      },
      {
        name: 'condition',
        label: '条件',
        type: DataType.BOOLEAN,
        direction: 'input',
        required: true,
        defaultValue: false,
        description: '等待的条件'
      },
      {
        name: 'timeout',
        label: '超时时间',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 10.0,
        description: '超时时间（秒）'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'completed',
        label: '完成',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '条件满足时执行'
      },
      {
        name: 'timeout',
        label: '超时',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '超时时执行'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const execTrigger = context.getInputValue('exec');
    const timeout = context.getInputValue('timeout') || 10.0;

    if (!execTrigger) return;

    const startTime = Date.now();
    const timeoutMs = timeout * 1000;

    while (Date.now() - startTime < timeoutMs) {
      if (context.getInputValue('condition')) {
        context.setOutputValue('completed', true);
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 10)); // 短暂等待
    }

    context.setOutputValue('timeout', true);
  }
}

/**
 * 018 - Try-Catch异常处理 (Try Catch)
 * 异常捕获和处理
 */
export class TryCatchNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/try-catch', 'Try-Catch异常处理', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始执行Try块'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'try',
        label: 'Try块',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '尝试执行的代码块'
      },
      {
        name: 'catch',
        label: 'Catch块',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '异常处理代码块'
      },
      {
        name: 'finally',
        label: 'Finally块',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '最终执行的代码块'
      },
      {
        name: 'error',
        label: '错误信息',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '捕获的错误信息'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    try {
      context.setOutputValue('try', true);
      // 这里应该等待try块的执行完成，但在当前架构下简化处理
    } catch (error) {
      context.setOutputValue('error', error instanceof Error ? error.message : String(error));
      context.setOutputValue('catch', true);
    } finally {
      context.setOutputValue('finally', true);
    }
  }
}

/**
 * 019 - 抛出异常 (Throw Exception)
 * 主动抛出异常
 */
export class ThrowExceptionNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/throw-exception', '抛出异常', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '触发异常抛出'
      },
      {
        name: 'message',
        label: '错误消息',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: '发生了一个错误',
        description: '异常消息'
      },
      {
        name: 'errorType',
        label: '错误类型',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'Error',
        description: '错误类型'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'thrown',
        label: '已抛出',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '异常抛出后触发'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const message = context.getInputValue('message') || '发生了一个错误';
    const errorType = context.getInputValue('errorType') || 'Error';

    context.log('error', `抛出异常: ${errorType} - ${message}`);
    context.setOutputValue('thrown', true);

    // 在实际实现中，这里应该抛出真正的异常
    // throw new Error(message);
  }
}

/**
 * 020 - 重试机制 (Retry)
 * 失败时重试执行
 */
export class RetryNode extends BaseNode {
  private retryCount = 0;

  constructor(id?: string) {
    super(id, 'program-logic/retry', '重试机制', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'exec',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '开始执行'
      },
      {
        name: 'success',
        label: '成功',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '执行成功信号'
      },
      {
        name: 'failure',
        label: '失败',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '执行失败信号'
      },
      {
        name: 'maxRetries',
        label: '最大重试次数',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 3,
        description: '最大重试次数'
      },
      {
        name: 'retryDelay',
        label: '重试延迟',
        type: DataType.NUMBER,
        direction: 'input',
        required: false,
        defaultValue: 1.0,
        description: '重试延迟时间（秒）'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'attempt',
        label: '尝试',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '每次尝试执行'
      },
      {
        name: 'success',
        label: '成功',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '最终成功'
      },
      {
        name: 'failed',
        label: '失败',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '重试次数用尽后失败'
      },
      {
        name: 'attemptCount',
        label: '尝试次数',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前尝试次数'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const execTrigger = context.getInputValue('exec');
    const successTrigger = context.getInputValue('success');
    const failureTrigger = context.getInputValue('failure');
    const maxRetries = context.getInputValue('maxRetries') || 3;
    const retryDelay = context.getInputValue('retryDelay') || 1.0;

    if (execTrigger) {
      this.retryCount = 0;
      context.setOutputValue('attempt', true);
      context.setOutputValue('attemptCount', this.retryCount + 1);
    }

    if (successTrigger) {
      context.setOutputValue('success', true);
      this.retryCount = 0;
    }

    if (failureTrigger) {
      this.retryCount++;
      if (this.retryCount < maxRetries) {
        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * 1000));
        context.setOutputValue('attempt', true);
        context.setOutputValue('attemptCount', this.retryCount + 1);
      } else {
        context.setOutputValue('failed', true);
        this.retryCount = 0;
      }
    }
  }
}
