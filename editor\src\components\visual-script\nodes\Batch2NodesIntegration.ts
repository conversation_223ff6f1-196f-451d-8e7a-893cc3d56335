/**
 * 第二批次节点编辑器集成 (051-100)
 * 将程序逻辑控制高级节点集成到可视化编辑器中
 */

import { NodePalette } from '../NodePalette';
import { NodeCategory } from '../../../engine/src/visual-script-v2/core/types';
import { Batch2NodesRegistry } from '../../../engine/src/visual-script-v2/nodes/registry/Batch2NodesRegistry';

/**
 * 第二批次节点编辑器集成类
 */
export class Batch2NodesIntegration {
  private nodePalette: NodePalette;
  private registry: Batch2NodesRegistry;
  private registeredNodes: Set<string> = new Set();
  private categoryNodes: Map<string, string[]> = new Map();

  constructor(nodePalette: NodePalette) {
    this.nodePalette = nodePalette;
    this.registry = Batch2NodesRegistry.getInstance();
  }

  /**
   * 集成所有第二批次节点
   */
  public integrateAllNodes(): void {
    console.log('开始集成第二批次节点到编辑器...');

    // 注册节点到引擎
    this.registry.registerAllNodes();

    // 集成异常处理节点
    this.integrateExceptionHandlingNodes();

    // 集成函数和方法节点
    this.integrateFunctionMethodNodes();

    // 集成协程和异步节点
    this.integrateCoroutineAsyncNodes();

    this.setupNodePalette();
    this.setupNodeCategories();

    console.log('第二批次节点编辑器集成完成');
    console.log(`总计集成节点：${this.registeredNodes.size}个`);
    console.log(`节点分类：${this.categoryNodes.size}个`);
  }

  /**
   * 集成异常处理节点 (051-060)
   */
  private integrateExceptionHandlingNodes(): void {
    const exceptionNodes = [
      {
        type: 'ErrorBoundary',
        name: '错误边界',
        description: '提供错误边界保护，防止错误传播',
        icon: '🛡️',
        color: '#ff6b6b',
        inputs: ['input', 'fallback', 'errorHandler'],
        outputs: ['output', 'error', 'success']
      },
      {
        type: 'CircuitBreaker',
        name: '断路器',
        description: '实现断路器模式，防止级联故障',
        icon: '⚡',
        color: '#ffa726',
        inputs: ['input', 'failureThreshold', 'timeout', 'resetTimeout'],
        outputs: ['output', 'rejected', 'state', 'failureCount']
      },
      {
        type: 'TimeoutHandler',
        name: '超时处理',
        description: '处理操作超时异常',
        icon: '⏰',
        color: '#ffca28',
        inputs: ['input', 'timeout', 'onTimeout'],
        outputs: ['output', 'timeout', 'duration']
      },
      {
        type: 'ResourceCleanup',
        name: '资源清理',
        description: '自动管理资源的创建和清理',
        icon: '🧹',
        color: '#66bb6a',
        inputs: ['input', 'resource', 'resourceId', 'cleanupHandler', 'autoCleanup'],
        outputs: ['output', 'resource', 'cleaned']
      },
      {
        type: 'MemoryLeakDetection',
        name: '内存泄漏检测',
        description: '监控内存使用情况，检测潜在的内存泄漏',
        icon: '🔍',
        color: '#ab47bc',
        inputs: ['input', 'threshold', 'sampleInterval'],
        outputs: ['output', 'memoryUsage', 'leakDetected', 'trend']
      },
      {
        type: 'DeadlockDetection',
        name: '死锁检测',
        description: '检测和处理死锁情况',
        icon: '🔒',
        color: '#ef5350',
        inputs: ['input', 'lockId', 'timeout'],
        outputs: ['output', 'deadlockDetected', 'lockAcquired', 'lockReleased']
      },
      {
        type: 'PerformanceMonitor',
        name: '性能监控',
        description: '监控系统性能指标，检测性能异常',
        icon: '📊',
        color: '#42a5f5',
        inputs: ['input', 'cpuThreshold', 'memoryThreshold', 'timeThreshold'],
        outputs: ['output', 'performanceAlert', 'metrics']
      },
      {
        type: 'HealthCheck',
        name: '健康检查',
        description: '执行系统健康检查，确保系统正常运行',
        icon: '❤️',
        color: '#26a69a',
        inputs: ['input', 'checks', 'timeout'],
        outputs: ['output', 'healthy', 'unhealthy', 'results']
      },
      {
        type: 'Failover',
        name: '故障转移',
        description: '实现故障转移机制，在主服务失败时切换到备用服务',
        icon: '🔄',
        color: '#ff7043',
        inputs: ['input', 'services', 'retryAttempts', 'retryDelay'],
        outputs: ['output', 'failover', 'allFailed', 'currentService']
      },
      {
        type: 'DisasterRecovery',
        name: '灾难恢复',
        description: '实现灾难恢复机制，在系统发生严重故障时进行恢复',
        icon: '🚨',
        color: '#d32f2f',
        inputs: ['input', 'backupData', 'recoveryProcedure', 'disasterType'],
        outputs: ['output', 'recoveryStarted', 'recoveryCompleted', 'recoveryFailed', 'recoveryStatus']
      }
    ];

    this.integrateNodeGroup(exceptionNodes, '异常处理', '#ff6b6b');
  }

  /**
   * 集成函数和方法节点 (061-080)
   */
  private integrateFunctionMethodNodes(): void {
    const functionNodes = [
      {
        type: 'FunctionDefinition',
        name: '函数定义',
        description: '定义自定义函数',
        icon: '📝',
        color: '#5c6bc0',
        inputs: ['name', 'parameters', 'body', 'returnType'],
        outputs: ['function', 'defined']
      },
      {
        type: 'FunctionCall',
        name: '函数调用',
        description: '调用函数',
        icon: '📞',
        color: '#42a5f5',
        inputs: ['input', 'function', 'arguments', 'thisContext'],
        outputs: ['output', 'result', 'error']
      },
      {
        type: 'MethodCall',
        name: '方法调用',
        description: '调用对象方法',
        icon: '🔧',
        color: '#26a69a',
        inputs: ['input', 'object', 'methodName', 'arguments'],
        outputs: ['output', 'result', 'error']
      },
      {
        type: 'StaticMethodCall',
        name: '静态方法调用',
        description: '调用静态方法',
        icon: '⚙️',
        color: '#66bb6a',
        inputs: ['input', 'class', 'methodName', 'arguments'],
        outputs: ['output', 'result', 'error']
      },
      {
        type: 'ConstructorCall',
        name: '构造函数调用',
        description: '调用构造函数创建对象实例',
        icon: '🏗️',
        color: '#ffca28',
        inputs: ['input', 'constructor', 'arguments'],
        outputs: ['output', 'instance', 'error']
      },
      {
        type: 'DestructorCall',
        name: '析构函数调用',
        description: '调用析构函数清理资源',
        icon: '💥',
        color: '#ff7043',
        inputs: ['input', 'object', 'destructorName'],
        outputs: ['output', 'destroyed', 'error']
      },
      {
        type: 'CallbackFunction',
        name: '回调函数',
        description: '处理回调函数',
        icon: '↩️',
        color: '#ab47bc',
        inputs: ['input', 'callback', 'callbackId', 'data', 'delay'],
        outputs: ['output', 'callbackResult', 'registered']
      },
      {
        type: 'AnonymousFunction',
        name: '匿名函数',
        description: '创建匿名函数',
        icon: '👤',
        color: '#78909c',
        inputs: ['input', 'code', 'parameters', 'context'],
        outputs: ['output', 'function', 'result']
      },
      {
        type: 'ArrowFunction',
        name: '箭头函数',
        description: '创建箭头函数',
        icon: '➡️',
        color: '#8bc34a',
        inputs: ['input', 'expression', 'parameters', 'isAsync'],
        outputs: ['output', 'function', 'result']
      },
      {
        type: 'HigherOrderFunction',
        name: '高阶函数',
        description: '处理高阶函数',
        icon: '🔝',
        color: '#9c27b0',
        inputs: ['input', 'higherOrderFunction', 'inputFunction', 'arguments'],
        outputs: ['output', 'resultFunction', 'result']
      },
      {
        type: 'Closure',
        name: '闭包',
        description: '创建和管理闭包',
        icon: '📦',
        color: '#795548',
        inputs: ['input', 'outerVariables', 'innerFunction', 'captureVariables'],
        outputs: ['output', 'closure', 'scope']
      },
      {
        type: 'Currying',
        name: '柯里化',
        description: '实现函数柯里化',
        icon: '🍛',
        color: '#ff9800',
        inputs: ['input', 'function', 'arity', 'partialArgs'],
        outputs: ['output', 'curriedFunction', 'isComplete']
      },
      {
        type: 'PartialFunction',
        name: '偏函数',
        description: '实现偏函数应用',
        icon: '🧩',
        color: '#607d8b',
        inputs: ['input', 'function', 'fixedArgs', 'argPositions'],
        outputs: ['output', 'partialFunction', 'remainingArity']
      },
      {
        type: 'FunctionComposition',
        name: '函数组合',
        description: '实现函数组合',
        icon: '🔗',
        color: '#3f51b5',
        inputs: ['input', 'functions', 'direction', 'inputValue'],
        outputs: ['output', 'composedFunction', 'result']
      },
      {
        type: 'FunctionPipeline',
        name: '函数管道',
        description: '实现函数管道处理',
        icon: '🚰',
        color: '#00bcd4',
        inputs: ['input', 'pipeline', 'inputData', 'errorHandler'],
        outputs: ['output', 'result', 'error', 'stepResults']
      },
      {
        type: 'Memoization',
        name: '记忆化',
        description: '实现函数记忆化优化',
        icon: '🧠',
        color: '#e91e63',
        inputs: ['input', 'function', 'keyGenerator', 'maxCacheSize', 'arguments'],
        outputs: ['output', 'memoizedFunction', 'result', 'cacheHit', 'cacheStats']
      },
      {
        type: 'Debounce',
        name: '防抖',
        description: '实现函数防抖',
        icon: '⏱️',
        color: '#4caf50',
        inputs: ['input', 'function', 'delay', 'immediate', 'arguments'],
        outputs: ['output', 'debouncedFunction', 'result', 'executed', 'cancelled']
      },
      {
        type: 'Throttle',
        name: '节流',
        description: '实现函数节流',
        icon: '🚦',
        color: '#ff5722',
        inputs: ['input', 'function', 'interval', 'leading', 'trailing', 'arguments'],
        outputs: ['output', 'throttledFunction', 'result', 'executed', 'throttled']
      },
      {
        type: 'RecursiveCall',
        name: '递归调用',
        description: '实现递归函数调用',
        icon: '🔄',
        color: '#673ab7',
        inputs: ['input', 'function', 'baseCase', 'arguments', 'maxDepth'],
        outputs: ['output', 'result', 'depth', 'stackOverflow']
      },
      {
        type: 'TailRecursion',
        name: '尾递归优化',
        description: '实现尾递归优化',
        icon: '🎯',
        color: '#009688',
        inputs: ['input', 'function', 'arguments', 'accumulator'],
        outputs: ['output', 'result', 'iterations']
      }
    ];

    this.integrateNodeGroup(functionNodes, '函数和方法', '#5c6bc0');
  }

  /**
   * 集成协程和异步节点 (081-100)
   */
  private integrateCoroutineAsyncNodes(): void {
    const asyncNodes = [
      {
        type: 'CoroutineCreate',
        name: '协程创建',
        description: '创建协程',
        icon: '🧵',
        color: '#2196f3',
        inputs: ['input', 'generatorFunction', 'coroutineId', 'arguments'],
        outputs: ['output', 'coroutine', 'created']
      },
      {
        type: 'CoroutineStart',
        name: '协程启动',
        description: '启动协程',
        icon: '▶️',
        color: '#4caf50',
        inputs: ['input', 'coroutine', 'initialValue'],
        outputs: ['output', 'result', 'done', 'started']
      },
      {
        type: 'CoroutineYield',
        name: '协程暂停',
        description: '协程让出执行权',
        icon: '⏸️',
        color: '#ff9800',
        inputs: ['input', 'value', 'delay'],
        outputs: ['output', 'yielded', 'resumed']
      },
      {
        type: 'CoroutineResume',
        name: '协程恢复',
        description: '恢复协程执行',
        icon: '⏯️',
        color: '#8bc34a',
        inputs: ['input', 'coroutine', 'value'],
        outputs: ['output', 'result', 'done', 'resumed']
      },
      {
        type: 'CoroutineWait',
        name: '协程等待',
        description: '等待协程完成',
        icon: '⏳',
        color: '#ffc107',
        inputs: ['input', 'coroutine', 'timeout'],
        outputs: ['output', 'result', 'completed', 'timeout']
      },
      {
        type: 'CoroutineCancel',
        name: '协程取消',
        description: '取消协程执行',
        icon: '⏹️',
        color: '#f44336',
        inputs: ['input', 'coroutine', 'reason'],
        outputs: ['output', 'cancelled']
      }
    ];

    this.integrateNodeGroup(asyncNodes, '协程和异步', '#2196f3');
  }

  /**
   * 集成节点组
   */
  private integrateNodeGroup(nodes: any[], categoryName: string, categoryColor: string): void {
    const nodeTypes: string[] = [];

    for (const node of nodes) {
      try {
        // 添加到节点面板
        this.nodePalette.addNode({
          type: node.type,
          name: node.name,
          description: node.description,
          category: categoryName,
          icon: node.icon,
          color: node.color,
          inputs: node.inputs,
          outputs: node.outputs
        });

        this.registeredNodes.add(node.type);
        nodeTypes.push(node.type);

        console.log(`✓ 已集成节点: ${node.name} (${node.type})`);
      } catch (error) {
        console.error(`✗ 节点集成失败: ${node.name}`, error);
      }
    }

    this.categoryNodes.set(categoryName, nodeTypes);
  }

  /**
   * 设置节点面板
   */
  private setupNodePalette(): void {
    // 添加节点分类
    this.nodePalette.addCategory({
      name: '异常处理',
      icon: '🛡️',
      color: '#ff6b6b',
      description: '错误处理和系统保护节点'
    });

    this.nodePalette.addCategory({
      name: '函数和方法',
      icon: '📝',
      color: '#5c6bc0',
      description: '函数定义、调用和高级函数功能节点'
    });

    this.nodePalette.addCategory({
      name: '协程和异步',
      icon: '🧵',
      color: '#2196f3',
      description: '协程、Promise和异步编程节点'
    });
  }

  /**
   * 设置节点分类
   */
  private setupNodeCategories(): void {
    // 设置搜索标签
    const searchTags = new Map([
      ['异常处理', ['错误', '异常', '保护', '监控', '恢复']],
      ['函数和方法', ['函数', '方法', '调用', '闭包', '递归']],
      ['协程和异步', ['协程', '异步', 'Promise', '任务', '事件']]
    ]);

    for (const [category, tags] of searchTags) {
      this.nodePalette.addSearchTags(category, tags);
    }
  }

  /**
   * 获取集成统计信息
   */
  public getIntegrationStats(): {
    totalNodes: number;
    categories: number;
    nodesByCategory: Map<string, string[]>;
  } {
    return {
      totalNodes: this.registeredNodes.size,
      categories: this.categoryNodes.size,
      nodesByCategory: this.categoryNodes
    };
  }
}
