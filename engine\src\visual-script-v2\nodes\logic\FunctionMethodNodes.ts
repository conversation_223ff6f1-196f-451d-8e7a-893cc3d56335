/**
 * 函数和方法节点 (061-080)
 * 实现函数定义、调用、闭包、柯里化等高级函数功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 061 - 函数定义节点
 * 定义自定义函数
 */
export class FunctionDefinitionNode extends BaseNode {
  private definedFunction: Function | null = null;
  
  constructor() {
    super('FunctionDefinition', '函数定义', NodeCategory.LOGIC);
    
    this.addInputPort('name', '函数名', DataType.STRING);
    this.addInputPort('parameters', '参数', DataType.ARRAY);
    this.addInputPort('body', '函数体', DataType.FUNCTION);
    this.addInputPort('returnType', '返回类型', DataType.STRING);
    
    this.addOutputPort('function', '函数', DataType.FUNCTION);
    this.addOutputPort('defined', '已定义', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const name = this.getInputValue('name') || 'anonymous';
    const parameters = this.getInputValue('parameters') || [];
    const body = this.getInputValue('body');
    const returnType = this.getInputValue('returnType') || 'any';
    
    if (!body || typeof body !== 'function') {
      throw new Error('函数体必须是一个函数');
    }
    
    // 创建函数定义
    this.definedFunction = (...args: any[]) => {
      // 验证参数数量
      if (args.length !== parameters.length) {
        console.warn(`参数数量不匹配: 期望 ${parameters.length}, 实际 ${args.length}`);
      }
      
      // 创建参数映射
      const paramMap: any = {};
      parameters.forEach((param: any, index: number) => {
        const paramName = typeof param === 'string' ? param : param.name;
        paramMap[paramName] = args[index];
      });
      
      // 执行函数体
      return body(paramMap, context);
    };
    
    // 添加元数据
    Object.defineProperty(this.definedFunction, 'name', { value: name });
    (this.definedFunction as any).parameters = parameters;
    (this.definedFunction as any).returnType = returnType;
    (this.definedFunction as any).nodeId = this.id;
    
    this.setOutputValue('function', this.definedFunction);
    
    console.log(`函数已定义: ${name}(${parameters.join(', ')})`);
    await this.triggerOutput(context, 'defined');
  }
  
  getDefinedFunction(): Function | null {
    return this.definedFunction;
  }
}

/**
 * 062 - 函数调用节点
 * 调用函数
 */
export class FunctionCallNode extends BaseNode {
  constructor() {
    super('FunctionCall', '函数调用', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('thisContext', 'this上下文', DataType.OBJECT);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const args = this.getInputValue('arguments') || [];
    const thisContext = this.getInputValue('thisContext');
    
    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }
    
    try {
      let result: any;
      
      if (thisContext) {
        result = await func.apply(thisContext, args);
      } else {
        result = await func(...args);
      }
      
      this.setOutputValue('result', result);
      this.setOutputValue('error', null);
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('函数调用失败:', error);
      
      this.setOutputValue('result', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }
}

/**
 * 063 - 方法调用节点
 * 调用对象方法
 */
export class MethodCallNode extends BaseNode {
  constructor() {
    super('MethodCall', '方法调用', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('object', '对象', DataType.OBJECT);
    this.addInputPort('methodName', '方法名', DataType.STRING);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const obj = this.getInputValue('object');
    const methodName = this.getInputValue('methodName');
    const args = this.getInputValue('arguments') || [];
    
    if (!obj || typeof obj !== 'object') {
      throw new Error('必须提供一个有效的对象');
    }
    
    if (!methodName || typeof methodName !== 'string') {
      throw new Error('必须提供方法名');
    }
    
    if (!(methodName in obj) || typeof obj[methodName] !== 'function') {
      throw new Error(`对象上不存在方法: ${methodName}`);
    }
    
    try {
      const method = obj[methodName];
      const result = await method.apply(obj, args);
      
      this.setOutputValue('result', result);
      this.setOutputValue('error', null);
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error(`方法调用失败: ${methodName}`, error);
      
      this.setOutputValue('result', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        methodName,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }
}

/**
 * 064 - 静态方法调用节点
 * 调用静态方法
 */
export class StaticMethodCallNode extends BaseNode {
  constructor() {
    super('StaticMethodCall', '静态方法调用', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('class', '类', DataType.FUNCTION);
    this.addInputPort('methodName', '方法名', DataType.STRING);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const cls = this.getInputValue('class');
    const methodName = this.getInputValue('methodName');
    const args = this.getInputValue('arguments') || [];
    
    if (!cls || typeof cls !== 'function') {
      throw new Error('必须提供一个有效的类');
    }
    
    if (!methodName || typeof methodName !== 'string') {
      throw new Error('必须提供方法名');
    }
    
    if (!(methodName in cls) || typeof cls[methodName] !== 'function') {
      throw new Error(`类上不存在静态方法: ${methodName}`);
    }
    
    try {
      const method = cls[methodName];
      const result = await method.apply(cls, args);
      
      this.setOutputValue('result', result);
      this.setOutputValue('error', null);
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error(`静态方法调用失败: ${methodName}`, error);
      
      this.setOutputValue('result', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        methodName,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }
}

/**
 * 065 - 构造函数调用节点
 * 调用构造函数创建对象实例
 */
export class ConstructorCallNode extends BaseNode {
  constructor() {
    super('ConstructorCall', '构造函数调用', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('constructor', '构造函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('instance', '实例', DataType.OBJECT);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const constructor = this.getInputValue('constructor');
    const args = this.getInputValue('arguments') || [];
    
    if (!constructor || typeof constructor !== 'function') {
      throw new Error('必须提供一个有效的构造函数');
    }
    
    try {
      const instance = new constructor(...args);
      
      this.setOutputValue('instance', instance);
      this.setOutputValue('error', null);
      
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('构造函数调用失败:', error);
      
      this.setOutputValue('instance', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        constructorName: constructor.name,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }
}

/**
 * 067 - 回调函数节点
 * 处理回调函数
 */
export class CallbackFunctionNode extends BaseNode {
  private callbacks: Map<string, Function> = new Map();

  constructor() {
    super('CallbackFunction', '回调函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('callback', '回调函数', DataType.FUNCTION);
    this.addInputPort('callbackId', '回调ID', DataType.STRING);
    this.addInputPort('data', '数据', DataType.ANY);
    this.addInputPort('delay', '延迟', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('callbackResult', '回调结果', DataType.ANY);
    this.addOutputPort('registered', '已注册', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const callback = this.getInputValue('callback');
    const callbackId = this.getInputValue('callbackId') || `callback_${Date.now()}`;
    const data = this.getInputValue('data');
    const delay = this.getInputValue('delay') || 0;

    if (!callback || typeof callback !== 'function') {
      throw new Error('必须提供一个有效的回调函数');
    }

    // 注册回调函数
    this.callbacks.set(callbackId, callback);
    await this.triggerOutput(context, 'registered');

    try {
      // 延迟执行回调
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // 执行回调函数
      const result = await callback(data, context);

      this.setOutputValue('callbackResult', result);

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('回调函数执行失败:', error);
      throw error;
    }
  }

  // 手动触发回调
  async triggerCallback(callbackId: string, data: any, context: IExecutionContext): Promise<any> {
    const callback = this.callbacks.get(callbackId);
    if (callback) {
      return await callback(data, context);
    }
    throw new Error(`回调函数不存在: ${callbackId}`);
  }
}

/**
 * 068 - 匿名函数节点
 * 创建匿名函数
 */
export class AnonymousFunctionNode extends BaseNode {
  constructor() {
    super('AnonymousFunction', '匿名函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('code', '代码', DataType.STRING);
    this.addInputPort('parameters', '参数', DataType.ARRAY);
    this.addInputPort('context', '上下文', DataType.OBJECT);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('function', '函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const code = this.getInputValue('code');
    const parameters = this.getInputValue('parameters') || [];
    const functionContext = this.getInputValue('context') || {};

    if (!code || typeof code !== 'string') {
      throw new Error('必须提供函数代码');
    }

    try {
      // 创建匿名函数
      const paramNames = parameters.map((p: any) =>
        typeof p === 'string' ? p : p.name
      );

      const functionBody = `
        return (async function(${paramNames.join(', ')}) {
          ${code}
        });
      `;

      const anonymousFunction = new Function('context', functionBody)(functionContext);

      this.setOutputValue('function', anonymousFunction);

      // 如果有参数值，立即执行函数
      const paramValues = parameters.map((p: any) =>
        typeof p === 'object' && 'value' in p ? p.value : undefined
      );

      if (paramValues.some(v => v !== undefined)) {
        const result = await anonymousFunction(...paramValues);
        this.setOutputValue('result', result);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('匿名函数创建失败:', error);
      throw error;
    }
  }
}

/**
 * 069 - 箭头函数节点
 * 创建箭头函数
 */
export class ArrowFunctionNode extends BaseNode {
  constructor() {
    super('ArrowFunction', '箭头函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('expression', '表达式', DataType.STRING);
    this.addInputPort('parameters', '参数', DataType.ARRAY);
    this.addInputPort('isAsync', '异步', DataType.BOOLEAN);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('function', '函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const expression = this.getInputValue('expression');
    const parameters = this.getInputValue('parameters') || [];
    const isAsync = this.getInputValue('isAsync') || false;

    if (!expression || typeof expression !== 'string') {
      throw new Error('必须提供表达式');
    }

    try {
      // 构建箭头函数
      const paramNames = parameters.map((p: any) =>
        typeof p === 'string' ? p : p.name
      );

      const asyncKeyword = isAsync ? 'async ' : '';
      const functionCode = `${asyncKeyword}(${paramNames.join(', ')}) => ${expression}`;

      // 创建箭头函数
      const arrowFunction = new Function('return ' + functionCode)();

      this.setOutputValue('function', arrowFunction);

      // 如果有参数值，立即执行函数
      const paramValues = parameters.map((p: any) =>
        typeof p === 'object' && 'value' in p ? p.value : undefined
      );

      if (paramValues.some(v => v !== undefined)) {
        const result = isAsync
          ? await arrowFunction(...paramValues)
          : arrowFunction(...paramValues);
        this.setOutputValue('result', result);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('箭头函数创建失败:', error);
      throw error;
    }
  }
}

/**
 * 070 - 高阶函数节点
 * 处理高阶函数（接受函数作为参数或返回函数的函数）
 */
export class HigherOrderFunctionNode extends BaseNode {
  constructor() {
    super('HigherOrderFunction', '高阶函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('higherOrderFunction', '高阶函数', DataType.FUNCTION);
    this.addInputPort('inputFunction', '输入函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('resultFunction', '结果函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const higherOrderFunction = this.getInputValue('higherOrderFunction');
    const inputFunction = this.getInputValue('inputFunction');
    const args = this.getInputValue('arguments') || [];

    if (!higherOrderFunction || typeof higherOrderFunction !== 'function') {
      throw new Error('必须提供一个有效的高阶函数');
    }

    try {
      let result: any;

      if (inputFunction && typeof inputFunction === 'function') {
        // 将函数作为参数传递给高阶函数
        result = await higherOrderFunction(inputFunction, ...args);
      } else {
        // 直接调用高阶函数
        result = await higherOrderFunction(...args);
      }

      // 如果结果是函数，输出到resultFunction端口
      if (typeof result === 'function') {
        this.setOutputValue('resultFunction', result);
      } else {
        this.setOutputValue('result', result);
      }

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('高阶函数执行失败:', error);
      throw error;
    }
  }
}

/**
 * 071 - 闭包节点
 * 创建和管理闭包
 */
export class ClosureNode extends BaseNode {
  private closureScope: Map<string, any> = new Map();

  constructor() {
    super('Closure', '闭包', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('outerVariables', '外部变量', DataType.OBJECT);
    this.addInputPort('innerFunction', '内部函数', DataType.FUNCTION);
    this.addInputPort('captureVariables', '捕获变量', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('closure', '闭包', DataType.FUNCTION);
    this.addOutputPort('scope', '作用域', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const outerVariables = this.getInputValue('outerVariables') || {};
    const innerFunction = this.getInputValue('innerFunction');
    const captureVariables = this.getInputValue('captureVariables') || [];

    if (!innerFunction || typeof innerFunction !== 'function') {
      throw new Error('必须提供内部函数');
    }

    // 捕获外部变量到闭包作用域
    for (const varName of captureVariables) {
      if (varName in outerVariables) {
        this.closureScope.set(varName, outerVariables[varName]);
      }
    }

    // 创建闭包函数
    const closure = (...args: any[]) => {
      // 创建闭包执行上下文
      const closureContext = {
        ...context,
        scope: Object.fromEntries(this.closureScope),
        outerScope: outerVariables
      };

      return innerFunction.apply(closureContext, args);
    };

    // 添加闭包元数据
    (closure as any).scope = Object.fromEntries(this.closureScope);
    (closure as any).capturedVariables = captureVariables;
    (closure as any).nodeId = this.id;

    this.setOutputValue('closure', closure);
    this.setOutputValue('scope', Object.fromEntries(this.closureScope));

    await this.triggerOutput(context, 'output');
  }

  // 更新闭包作用域中的变量
  updateClosureVariable(name: string, value: any): void {
    this.closureScope.set(name, value);
  }

  // 获取闭包作用域中的变量
  getClosureVariable(name: string): any {
    return this.closureScope.get(name);
  }
}

/**
 * 072 - 柯里化节点
 * 实现函数柯里化
 */
export class CurryingNode extends BaseNode {
  constructor() {
    super('Currying', '柯里化', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('arity', '参数数量', DataType.NUMBER);
    this.addInputPort('partialArgs', '部分参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('curriedFunction', '柯里化函数', DataType.FUNCTION);
    this.addOutputPort('isComplete', '是否完成', DataType.BOOLEAN);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const arity = this.getInputValue('arity') || func?.length || 0;
    const partialArgs = this.getInputValue('partialArgs') || [];

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    // 创建柯里化函数
    const curriedFunction = this.curry(func, arity, partialArgs);

    this.setOutputValue('curriedFunction', curriedFunction);
    this.setOutputValue('isComplete', partialArgs.length >= arity);

    await this.triggerOutput(context, 'output');
  }

  private curry(func: Function, arity: number, partialArgs: any[] = []): Function {
    return (...args: any[]) => {
      const allArgs = [...partialArgs, ...args];

      if (allArgs.length >= arity) {
        // 参数足够，执行原函数
        return func.apply(this, allArgs.slice(0, arity));
      } else {
        // 参数不够，返回新的柯里化函数
        return this.curry(func, arity, allArgs);
      }
    };
  }
}

/**
 * 073 - 偏函数节点
 * 实现偏函数应用
 */
export class PartialFunctionNode extends BaseNode {
  constructor() {
    super('PartialFunction', '偏函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('fixedArgs', '固定参数', DataType.ARRAY);
    this.addInputPort('argPositions', '参数位置', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('partialFunction', '偏函数', DataType.FUNCTION);
    this.addOutputPort('remainingArity', '剩余参数数', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const fixedArgs = this.getInputValue('fixedArgs') || [];
    const argPositions = this.getInputValue('argPositions') || [];

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    // 创建偏函数
    const partialFunction = (...remainingArgs: any[]) => {
      const finalArgs: any[] = [];
      let remainingIndex = 0;
      let fixedIndex = 0;

      // 根据位置信息组装最终参数
      for (let i = 0; i < func.length; i++) {
        if (argPositions.includes(i)) {
          // 使用固定参数
          finalArgs[i] = fixedArgs[fixedIndex++];
        } else {
          // 使用剩余参数
          finalArgs[i] = remainingArgs[remainingIndex++];
        }
      }

      return func.apply(this, finalArgs);
    };

    const remainingArity = func.length - fixedArgs.length;

    // 添加元数据
    (partialFunction as any).originalFunction = func;
    (partialFunction as any).fixedArgs = fixedArgs;
    (partialFunction as any).remainingArity = remainingArity;

    this.setOutputValue('partialFunction', partialFunction);
    this.setOutputValue('remainingArity', remainingArity);

    await this.triggerOutput(context, 'output');
  }
}

/**
 * 074 - 函数组合节点
 * 实现函数组合
 */
export class FunctionCompositionNode extends BaseNode {
  constructor() {
    super('FunctionComposition', '函数组合', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('functions', '函数列表', DataType.ARRAY);
    this.addInputPort('direction', '组合方向', DataType.STRING);
    this.addInputPort('inputValue', '输入值', DataType.ANY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('composedFunction', '组合函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const functions = this.getInputValue('functions') || [];
    const direction = this.getInputValue('direction') || 'left-to-right'; // 'left-to-right' 或 'right-to-left'
    const inputValue = this.getInputValue('inputValue');

    if (!Array.isArray(functions) || functions.length === 0) {
      throw new Error('必须提供函数列表');
    }

    // 验证所有元素都是函数
    for (const func of functions) {
      if (typeof func !== 'function') {
        throw new Error('函数列表中包含非函数元素');
      }
    }

    // 创建组合函数
    const composedFunction = direction === 'right-to-left'
      ? this.composeRightToLeft(functions)
      : this.composeLeftToRight(functions);

    this.setOutputValue('composedFunction', composedFunction);

    // 如果提供了输入值，立即执行组合函数
    if (inputValue !== undefined) {
      try {
        const result = await composedFunction(inputValue);
        this.setOutputValue('result', result);
      } catch (error) {
        console.error('函数组合执行失败:', error);
        throw error;
      }
    }

    await this.triggerOutput(context, 'output');
  }

  private composeLeftToRight(functions: Function[]): Function {
    return async (input: any) => {
      let result = input;
      for (const func of functions) {
        result = await func(result);
      }
      return result;
    };
  }

  private composeRightToLeft(functions: Function[]): Function {
    return async (input: any) => {
      let result = input;
      for (let i = functions.length - 1; i >= 0; i--) {
        result = await functions[i](result);
      }
      return result;
    };
  }
}

/**
 * 075 - 函数管道节点
 * 实现函数管道处理
 */
export class FunctionPipelineNode extends BaseNode {
  constructor() {
    super('FunctionPipeline', '函数管道', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('pipeline', '管道函数', DataType.ARRAY);
    this.addInputPort('inputData', '输入数据', DataType.ANY);
    this.addInputPort('errorHandler', '错误处理器', DataType.FUNCTION);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('stepResults', '步骤结果', DataType.ARRAY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const pipeline = this.getInputValue('pipeline') || [];
    const inputData = this.getInputValue('inputData');
    const errorHandler = this.getInputValue('errorHandler');

    if (!Array.isArray(pipeline) || pipeline.length === 0) {
      throw new Error('必须提供管道函数列表');
    }

    const stepResults: any[] = [];
    let currentData = inputData;

    try {
      for (let i = 0; i < pipeline.length; i++) {
        const stepFunction = pipeline[i];

        if (typeof stepFunction !== 'function') {
          throw new Error(`管道步骤 ${i} 不是函数`);
        }

        try {
          currentData = await stepFunction(currentData, context);
          stepResults.push({
            step: i,
            input: i === 0 ? inputData : stepResults[i - 1]?.output,
            output: currentData,
            success: true,
            error: null
          });
        } catch (stepError) {
          stepResults.push({
            step: i,
            input: i === 0 ? inputData : stepResults[i - 1]?.output,
            output: null,
            success: false,
            error: stepError.message
          });

          // 如果有错误处理器，尝试恢复
          if (errorHandler && typeof errorHandler === 'function') {
            try {
              currentData = await errorHandler(stepError, currentData, i, context);
              stepResults[i].recovered = true;
              stepResults[i].output = currentData;
            } catch (handlerError) {
              throw new Error(`管道步骤 ${i} 失败且错误处理器也失败: ${stepError.message}`);
            }
          } else {
            throw new Error(`管道步骤 ${i} 失败: ${stepError.message}`);
          }
        }
      }

      this.setOutputValue('result', currentData);
      this.setOutputValue('stepResults', stepResults);
      this.setOutputValue('error', null);

      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('函数管道执行失败:', error);

      this.setOutputValue('result', null);
      this.setOutputValue('stepResults', stepResults);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });

      throw error;
    }
  }
}

/**
 * 076 - 记忆化节点
 * 实现函数记忆化优化
 */
export class MemoizationNode extends BaseNode {
  private cache: Map<string, any> = new Map();
  private maxCacheSize = 100;

  constructor() {
    super('Memoization', '记忆化', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('keyGenerator', '键生成器', DataType.FUNCTION);
    this.addInputPort('maxCacheSize', '最大缓存大小', DataType.NUMBER);
    this.addInputPort('arguments', '参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('memoizedFunction', '记忆化函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('cacheHit', '缓存命中', DataType.BOOLEAN);
    this.addOutputPort('cacheStats', '缓存统计', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const keyGenerator = this.getInputValue('keyGenerator');
    const maxCacheSize = this.getInputValue('maxCacheSize') || this.maxCacheSize;
    const args = this.getInputValue('arguments');

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    this.maxCacheSize = maxCacheSize;

    // 创建记忆化函数
    const memoizedFunction = async (...functionArgs: any[]) => {
      // 生成缓存键
      const cacheKey = keyGenerator
        ? await keyGenerator(...functionArgs)
        : JSON.stringify(functionArgs);

      // 检查缓存
      if (this.cache.has(cacheKey)) {
        const cachedResult = this.cache.get(cacheKey);
        this.setOutputValue('cacheHit', true);
        return cachedResult;
      }

      // 执行函数
      const result = await func(...functionArgs);

      // 缓存结果
      this.addToCache(cacheKey, result);
      this.setOutputValue('cacheHit', false);

      return result;
    };

    this.setOutputValue('memoizedFunction', memoizedFunction);

    // 如果提供了参数，立即执行
    if (args && Array.isArray(args)) {
      try {
        const result = await memoizedFunction(...args);
        this.setOutputValue('result', result);
      } catch (error) {
        console.error('记忆化函数执行失败:', error);
        throw error;
      }
    }

    // 输出缓存统计
    this.setOutputValue('cacheStats', {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.calculateHitRate()
    });

    await this.triggerOutput(context, 'output');
  }

  private addToCache(key: string, value: any): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, value);
  }

  private calculateHitRate(): number {
    // 简化的命中率计算
    return this.cache.size > 0 ? 0.8 : 0; // 示例值
  }

  // 清空缓存
  clearCache(): void {
    this.cache.clear();
  }
}

/**
 * 077 - 防抖节点
 * 实现函数防抖
 */
export class DebounceNode extends BaseNode {
  private timeoutId: NodeJS.Timeout | null = null;
  private lastArgs: any[] = [];

  constructor() {
    super('Debounce', '防抖', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);
    this.addInputPort('immediate', '立即执行', DataType.BOOLEAN);
    this.addInputPort('arguments', '参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('debouncedFunction', '防抖函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('executed', '已执行', DataType.TRIGGER);
    this.addOutputPort('cancelled', '已取消', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const delay = this.getInputValue('delay') || 300;
    const immediate = this.getInputValue('immediate') || false;
    const args = this.getInputValue('arguments') || [];

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    // 创建防抖函数
    const debouncedFunction = (...functionArgs: any[]) => {
      this.lastArgs = functionArgs;

      // 清除之前的定时器
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.triggerOutput(context, 'cancelled').catch(console.error);
      }

      // 立即执行模式
      if (immediate && !this.timeoutId) {
        this.executeFunction(func, functionArgs, context);
        return;
      }

      // 设置新的定时器
      this.timeoutId = setTimeout(() => {
        this.executeFunction(func, this.lastArgs, context);
        this.timeoutId = null;
      }, delay);
    };

    this.setOutputValue('debouncedFunction', debouncedFunction);

    // 如果提供了参数，立即调用防抖函数
    if (args.length > 0) {
      debouncedFunction(...args);
    }

    await this.triggerOutput(context, 'output');
  }

  private async executeFunction(func: Function, args: any[], context: IExecutionContext): Promise<void> {
    try {
      const result = await func(...args);
      this.setOutputValue('result', result);
      await this.triggerOutput(context, 'executed');
    } catch (error) {
      console.error('防抖函数执行失败:', error);
      throw error;
    }
  }

  // 取消防抖
  cancel(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  dispose(): void {
    this.cancel();
    super.dispose();
  }
}

/**
 * 078 - 节流节点
 * 实现函数节流
 */
export class ThrottleNode extends BaseNode {
  private lastExecuteTime = 0;
  private timeoutId: NodeJS.Timeout | null = null;
  private lastArgs: any[] = [];

  constructor() {
    super('Throttle', '节流', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '函数', DataType.FUNCTION);
    this.addInputPort('interval', '间隔时间', DataType.NUMBER);
    this.addInputPort('leading', '前缘执行', DataType.BOOLEAN);
    this.addInputPort('trailing', '后缘执行', DataType.BOOLEAN);
    this.addInputPort('arguments', '参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('throttledFunction', '节流函数', DataType.FUNCTION);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('executed', '已执行', DataType.TRIGGER);
    this.addOutputPort('throttled', '被节流', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const interval = this.getInputValue('interval') || 1000;
    const leading = this.getInputValue('leading') !== false;
    const trailing = this.getInputValue('trailing') !== false;
    const args = this.getInputValue('arguments') || [];

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    // 创建节流函数
    const throttledFunction = (...functionArgs: any[]) => {
      const now = Date.now();
      this.lastArgs = functionArgs;

      // 检查是否在节流间隔内
      const timeSinceLastExecute = now - this.lastExecuteTime;

      if (timeSinceLastExecute >= interval) {
        // 可以执行
        if (leading) {
          this.executeFunction(func, functionArgs, context);
          this.lastExecuteTime = now;
        }

        // 设置后缘执行
        if (trailing && !leading) {
          this.scheduleTrailingExecution(func, interval, context);
        }
      } else {
        // 被节流
        this.triggerOutput(context, 'throttled').catch(console.error);

        // 设置后缘执行
        if (trailing) {
          this.scheduleTrailingExecution(func, interval - timeSinceLastExecute, context);
        }
      }
    };

    this.setOutputValue('throttledFunction', throttledFunction);

    // 如果提供了参数，立即调用节流函数
    if (args.length > 0) {
      throttledFunction(...args);
    }

    await this.triggerOutput(context, 'output');
  }

  private scheduleTrailingExecution(func: Function, delay: number, context: IExecutionContext): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      this.executeFunction(func, this.lastArgs, context);
      this.lastExecuteTime = Date.now();
      this.timeoutId = null;
    }, delay);
  }

  private async executeFunction(func: Function, args: any[], context: IExecutionContext): Promise<void> {
    try {
      const result = await func(...args);
      this.setOutputValue('result', result);
      await this.triggerOutput(context, 'executed');
    } catch (error) {
      console.error('节流函数执行失败:', error);
      throw error;
    }
  }

  dispose(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    super.dispose();
  }
}

/**
 * 079 - 递归调用节点
 * 实现递归函数调用
 */
export class RecursiveCallNode extends BaseNode {
  private callStack: Array<{ args: any[]; depth: number }> = [];
  private maxDepth = 1000;

  constructor() {
    super('RecursiveCall', '递归调用', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '递归函数', DataType.FUNCTION);
    this.addInputPort('baseCase', '基础情况', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('maxDepth', '最大深度', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('depth', '递归深度', DataType.NUMBER);
    this.addOutputPort('stackOverflow', '栈溢出', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const baseCase = this.getInputValue('baseCase');
    const args = this.getInputValue('arguments') || [];
    const maxDepth = this.getInputValue('maxDepth') || this.maxDepth;

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供递归函数');
    }

    if (!baseCase || typeof baseCase !== 'function') {
      throw new Error('必须提供基础情况函数');
    }

    this.maxDepth = maxDepth;
    this.callStack = [];

    try {
      const result = await this.recursiveCall(func, baseCase, args, 0, context);

      this.setOutputValue('result', result);
      this.setOutputValue('depth', this.callStack.length);

      await this.triggerOutput(context, 'output');

    } catch (error) {
      if (error.message.includes('栈溢出')) {
        await this.triggerOutput(context, 'stackOverflow');
      }
      throw error;
    }
  }

  private async recursiveCall(
    func: Function,
    baseCase: Function,
    args: any[],
    depth: number,
    context: IExecutionContext
  ): Promise<any> {
    // 检查栈溢出
    if (depth >= this.maxDepth) {
      throw new Error(`递归栈溢出: 深度超过 ${this.maxDepth}`);
    }

    // 记录调用栈
    this.callStack.push({ args: [...args], depth });

    try {
      // 检查基础情况
      const shouldStop = await baseCase(...args, depth, context);

      if (shouldStop) {
        // 达到基础情况，返回结果
        return shouldStop === true ? args[0] : shouldStop;
      }

      // 递归调用
      const result = await func(...args, depth, context);

      // 如果结果是数组，假设它是新的参数
      if (Array.isArray(result)) {
        return await this.recursiveCall(func, baseCase, result, depth + 1, context);
      } else {
        return result;
      }

    } finally {
      this.callStack.pop();
    }
  }

  getCallStack(): Array<{ args: any[]; depth: number }> {
    return [...this.callStack];
  }
}

/**
 * 080 - 尾递归优化节点
 * 实现尾递归优化
 */
export class TailRecursionNode extends BaseNode {
  constructor() {
    super('TailRecursion', '尾递归优化', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('function', '尾递归函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('accumulator', '累加器', DataType.ANY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('iterations', '迭代次数', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const func = this.getInputValue('function');
    const args = this.getInputValue('arguments') || [];
    let accumulator = this.getInputValue('accumulator');

    if (!func || typeof func !== 'function') {
      throw new Error('必须提供尾递归函数');
    }

    let iterations = 0;
    let currentArgs = [...args];

    // 使用循环模拟尾递归优化
    while (true) {
      iterations++;

      // 调用函数
      const result = await func(...currentArgs, accumulator, context);

      // 检查是否是尾递归调用
      if (result && typeof result === 'object' && result.isTailCall) {
        // 继续迭代
        currentArgs = result.args || currentArgs;
        accumulator = result.accumulator !== undefined ? result.accumulator : accumulator;

        // 防止无限循环
        if (iterations > 10000) {
          throw new Error('尾递归迭代次数过多，可能存在无限循环');
        }
      } else {
        // 返回最终结果
        this.setOutputValue('result', result);
        this.setOutputValue('iterations', iterations);
        break;
      }
    }

    await this.triggerOutput(context, 'output');
  }
}

/**
 * 066 - 析构函数调用节点
 * 调用析构函数清理资源
 */
export class DestructorCallNode extends BaseNode {
  constructor() {
    super('DestructorCall', '析构函数调用', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('object', '对象', DataType.OBJECT);
    this.addInputPort('destructorName', '析构函数名', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('destroyed', '已销毁', DataType.TRIGGER);
    this.addOutputPort('error', '错误', DataType.OBJECT);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const obj = this.getInputValue('object');
    const destructorName = this.getInputValue('destructorName') || 'dispose';
    
    if (!obj || typeof obj !== 'object') {
      throw new Error('必须提供一个有效的对象');
    }
    
    try {
      // 检查对象是否有析构函数
      if (destructorName in obj && typeof obj[destructorName] === 'function') {
        await obj[destructorName]();
        console.log(`对象已销毁: ${destructorName}`);
      } else {
        console.warn(`对象没有析构函数: ${destructorName}`);
      }
      
      this.setOutputValue('error', null);
      
      await this.triggerOutput(context, 'destroyed');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('析构函数调用失败:', error);
      
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        destructorName,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }
}
