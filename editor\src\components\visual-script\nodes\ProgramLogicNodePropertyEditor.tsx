/**
 * 第一批次：程序逻辑控制基础节点属性编辑器 (001-050)
 * 提供程序逻辑控制节点的属性编辑功能
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  IconButton,
  Tooltip,
  Divider,
  Alert
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Info as InfoIcon
} from '@mui/icons-material';

import { ProgramLogicNodeType } from './ProgramLogicNodesIntegration';
import { DataType } from '../../../../engine/src/visual-script-v2/core/types';

interface NodeProperty {
  name: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'slider' | 'array';
  value: any;
  defaultValue?: any;
  options?: any[];
  min?: number;
  max?: number;
  step?: number;
  description?: string;
  required?: boolean;
}

interface ProgramLogicNodePropertyEditorProps {
  node: ProgramLogicNodeType;
  properties: Record<string, any>;
  onPropertyChange: (propertyName: string, value: any) => void;
  onSave?: () => void;
  onReset?: () => void;
  isReadOnly?: boolean;
}

export const ProgramLogicNodePropertyEditor: React.FC<ProgramLogicNodePropertyEditorProps> = ({
  node,
  properties,
  onPropertyChange,
  onSave,
  onReset,
  isReadOnly = false
}) => {
  const [localProperties, setLocalProperties] = useState<Record<string, any>>(properties);
  const [hasChanges, setHasChanges] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>(['basic', 'inputs', 'outputs']);

  // 同步外部属性变化
  useEffect(() => {
    setLocalProperties(properties);
    setHasChanges(false);
  }, [properties]);

  // 获取节点的默认属性
  const getDefaultProperties = useCallback((): NodeProperty[] => {
    const defaultProps: NodeProperty[] = [
      {
        name: 'name',
        label: '节点名称',
        type: 'string',
        value: localProperties.name || node.name,
        defaultValue: node.name,
        description: '节点的显示名称',
        required: true
      },
      {
        name: 'description',
        label: '节点描述',
        type: 'string',
        value: localProperties.description || node.description,
        defaultValue: node.description,
        description: '节点的功能描述'
      },
      {
        name: 'enabled',
        label: '启用节点',
        type: 'boolean',
        value: localProperties.enabled !== undefined ? localProperties.enabled : true,
        defaultValue: true,
        description: '是否启用此节点'
      }
    ];

    // 根据节点类型添加特定属性
    switch (node.nodeType) {
      case 'program-logic/delay':
        defaultProps.push({
          name: 'duration',
          label: '延迟时间（秒）',
          type: 'slider',
          value: localProperties.duration || 1.0,
          defaultValue: 1.0,
          min: 0.1,
          max: 60.0,
          step: 0.1,
          description: '延迟执行的时间'
        });
        break;

      case 'program-logic/for-loop':
        defaultProps.push(
          {
            name: 'startIndex',
            label: '起始索引',
            type: 'number',
            value: localProperties.startIndex || 0,
            defaultValue: 0,
            description: '循环的起始索引'
          },
          {
            name: 'endIndex',
            label: '结束索引',
            type: 'number',
            value: localProperties.endIndex || 10,
            defaultValue: 10,
            description: '循环的结束索引'
          },
          {
            name: 'step',
            label: '步长',
            type: 'number',
            value: localProperties.step || 1,
            defaultValue: 1,
            description: '循环的步长'
          }
        );
        break;

      case 'program-logic/while-loop':
        defaultProps.push({
          name: 'maxIterations',
          label: '最大迭代次数',
          type: 'number',
          value: localProperties.maxIterations || 1000,
          defaultValue: 1000,
          description: '防止无限循环的最大迭代次数'
        });
        break;

      case 'program-logic/switch':
        defaultProps.push({
          name: 'caseCount',
          label: '分支数量',
          type: 'select',
          value: localProperties.caseCount || 3,
          defaultValue: 3,
          options: [2, 3, 4, 5, 6],
          description: '多路分支的数量'
        });
        break;

      case 'program-logic/retry':
        defaultProps.push(
          {
            name: 'maxRetries',
            label: '最大重试次数',
            type: 'number',
            value: localProperties.maxRetries || 3,
            defaultValue: 3,
            description: '最大重试次数'
          },
          {
            name: 'retryDelay',
            label: '重试延迟（秒）',
            type: 'slider',
            value: localProperties.retryDelay || 1.0,
            defaultValue: 1.0,
            min: 0.1,
            max: 10.0,
            step: 0.1,
            description: '重试之间的延迟时间'
          }
        );
        break;

      case 'program-logic/wait-until':
        defaultProps.push({
          name: 'timeout',
          label: '超时时间（秒）',
          type: 'slider',
          value: localProperties.timeout || 10.0,
          defaultValue: 10.0,
          min: 1.0,
          max: 300.0,
          step: 1.0,
          description: '等待的超时时间'
        });
        break;
    }

    return defaultProps;
  }, [node, localProperties]);

  // 处理属性变化
  const handlePropertyChange = useCallback((propertyName: string, value: any) => {
    const newProperties = { ...localProperties, [propertyName]: value };
    setLocalProperties(newProperties);
    setHasChanges(true);
    onPropertyChange(propertyName, value);
  }, [localProperties, onPropertyChange]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave();
    }
    setHasChanges(false);
  }, [onSave]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (onReset) {
      onReset();
    }
    setLocalProperties(properties);
    setHasChanges(false);
  }, [onReset, properties]);

  // 处理部分展开/折叠
  const handleSectionToggle = useCallback((section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  }, []);

  // 渲染属性编辑器
  const renderPropertyEditor = useCallback((property: NodeProperty) => {
    const { name, label, type, value, options, min, max, step, description, required } = property;

    switch (type) {
      case 'string':
        return (
          <TextField
            fullWidth
            label={label}
            value={value || ''}
            onChange={(e) => handlePropertyChange(name, e.target.value)}
            disabled={isReadOnly}
            required={required}
            helperText={description}
            size="small"
          />
        );

      case 'number':
        return (
          <TextField
            fullWidth
            label={label}
            type="number"
            value={value || 0}
            onChange={(e) => handlePropertyChange(name, parseFloat(e.target.value) || 0)}
            disabled={isReadOnly}
            required={required}
            helperText={description}
            size="small"
            inputProps={{ min, max, step }}
          />
        );

      case 'boolean':
        return (
          <FormControlLabel
            control={
              <Switch
                checked={Boolean(value)}
                onChange={(e) => handlePropertyChange(name, e.target.checked)}
                disabled={isReadOnly}
              />
            }
            label={
              <Box>
                <Typography variant="body2">{label}</Typography>
                {description && (
                  <Typography variant="caption" color="text.secondary">
                    {description}
                  </Typography>
                )}
              </Box>
            }
          />
        );

      case 'select':
        return (
          <FormControl fullWidth size="small">
            <InputLabel>{label}</InputLabel>
            <Select
              value={value}
              label={label}
              onChange={(e) => handlePropertyChange(name, e.target.value)}
              disabled={isReadOnly}
            >
              {options?.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            {description && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                {description}
              </Typography>
            )}
          </FormControl>
        );

      case 'slider':
        return (
          <Box>
            <Typography variant="body2" gutterBottom>
              {label}: {value}
            </Typography>
            <Slider
              value={value || min || 0}
              onChange={(_, newValue) => handlePropertyChange(name, newValue)}
              disabled={isReadOnly}
              min={min}
              max={max}
              step={step}
              marks
              valueLabelDisplay="auto"
            />
            {description && (
              <Typography variant="caption" color="text.secondary">
                {description}
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  }, [handlePropertyChange, isReadOnly]);

  const defaultProperties = getDefaultProperties();

  return (
    <Box sx={{ width: '100%', maxWidth: 400 }}>
      {/* 标题栏 */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Box sx={{ fontSize: '1.2rem' }}>{node.icon}</Box>
          <Typography variant="h6" noWrap>
            {node.name}
          </Typography>
        </Box>
        <Typography variant="caption" color="text.secondary">
          {node.id} - {node.nodeType}
        </Typography>
        
        {hasChanges && (
          <Alert severity="info" sx={{ mt: 1 }}>
            有未保存的更改
          </Alert>
        )}
      </Box>

      {/* 操作按钮 */}
      {!isReadOnly && (
        <Box sx={{ p: 2, display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            size="small"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={!hasChanges}
          >
            保存
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RestoreIcon />}
            onClick={handleReset}
            disabled={!hasChanges}
          >
            重置
          </Button>
        </Box>
      )}

      {/* 基础属性 */}
      <Accordion
        expanded={expandedSections.includes('basic')}
        onChange={() => handleSectionToggle('basic')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">基础属性</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {defaultProperties.map((property) => (
              <Box key={property.name}>
                {renderPropertyEditor(property)}
              </Box>
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* 输入端口 */}
      <Accordion
        expanded={expandedSections.includes('inputs')}
        onChange={() => handleSectionToggle('inputs')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">
            输入端口 ({node.inputs.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {node.inputs.map((input, index) => (
              <Box key={index} sx={{ p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="body2" fontWeight="medium">
                  {input.label} ({input.name})
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  类型: {input.type} | 必需: {input.required ? '是' : '否'}
                </Typography>
                {input.description && (
                  <Typography variant="caption" display="block" color="text.secondary">
                    {input.description}
                  </Typography>
                )}
              </Box>
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* 输出端口 */}
      <Accordion
        expanded={expandedSections.includes('outputs')}
        onChange={() => handleSectionToggle('outputs')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">
            输出端口 ({node.outputs.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {node.outputs.map((output, index) => (
              <Box key={index} sx={{ p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="body2" fontWeight="medium">
                  {output.label} ({output.name})
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  类型: {output.type} | 必需: {output.required ? '是' : '否'}
                </Typography>
                {output.description && (
                  <Typography variant="caption" display="block" color="text.secondary">
                    {output.description}
                  </Typography>
                )}
              </Box>
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* 节点信息 */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Typography variant="caption" color="text.secondary">
          分类: {node.category}
        </Typography>
        <br />
        <Typography variant="caption" color="text.secondary">
          标签: {node.tags.join(', ')}
        </Typography>
      </Box>
    </Box>
  );
};

export default ProgramLogicNodePropertyEditor;
