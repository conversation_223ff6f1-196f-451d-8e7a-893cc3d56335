/**
 * 协程和异步节点 (081-100)
 * 实现协程创建、Promise处理、异步迭代器等异步编程功能
 */

import { BaseNode } from '../base/BaseNode';
import { NodeCategory, DataType, NodePort, IExecutionContext } from '../../core/types';

/**
 * 081 - 协程创建节点
 * 创建协程
 */
export class CoroutineCreateNode extends BaseNode {
  private coroutines: Map<string, Generator> = new Map();
  
  constructor() {
    super('CoroutineCreate', '协程创建', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('generatorFunction', '生成器函数', DataType.FUNCTION);
    this.addInputPort('coroutineId', '协程ID', DataType.STRING);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('coroutine', '协程', DataType.OBJECT);
    this.addOutputPort('created', '已创建', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const generatorFunction = this.getInputValue('generatorFunction');
    const coroutineId = this.getInputValue('coroutineId') || `coroutine_${Date.now()}`;
    const args = this.getInputValue('arguments') || [];
    
    if (!generatorFunction || typeof generatorFunction !== 'function') {
      throw new Error('必须提供生成器函数');
    }
    
    try {
      // 创建协程（生成器实例）
      const coroutine = generatorFunction(...args);
      
      if (!coroutine || typeof coroutine.next !== 'function') {
        throw new Error('提供的函数不是生成器函数');
      }
      
      // 存储协程
      this.coroutines.set(coroutineId, coroutine);
      
      // 创建协程包装对象
      const coroutineWrapper = {
        id: coroutineId,
        generator: coroutine,
        status: 'created',
        createdAt: Date.now(),
        lastYieldValue: null,
        isDone: false
      };
      
      this.setOutputValue('coroutine', coroutineWrapper);
      
      console.log(`协程已创建: ${coroutineId}`);
      await this.triggerOutput(context, 'created');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('协程创建失败:', error);
      throw error;
    }
  }
  
  getCoroutine(id: string): Generator | null {
    return this.coroutines.get(id) || null;
  }
  
  removeCoroutine(id: string): boolean {
    return this.coroutines.delete(id);
  }
}

/**
 * 082 - 协程启动节点
 * 启动协程
 */
export class CoroutineStartNode extends BaseNode {
  constructor() {
    super('CoroutineStart', '协程启动', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('coroutine', '协程', DataType.OBJECT);
    this.addInputPort('initialValue', '初始值', DataType.ANY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('done', '完成', DataType.BOOLEAN);
    this.addOutputPort('started', '已启动', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const coroutineWrapper = this.getInputValue('coroutine');
    const initialValue = this.getInputValue('initialValue');
    
    if (!coroutineWrapper || !coroutineWrapper.generator) {
      throw new Error('必须提供有效的协程对象');
    }
    
    const generator = coroutineWrapper.generator;
    
    try {
      // 启动协程
      const result = generator.next(initialValue);
      
      // 更新协程状态
      coroutineWrapper.status = 'running';
      coroutineWrapper.lastYieldValue = result.value;
      coroutineWrapper.isDone = result.done;
      
      this.setOutputValue('result', result.value);
      this.setOutputValue('done', result.done);
      
      console.log(`协程已启动: ${coroutineWrapper.id}`);
      await this.triggerOutput(context, 'started');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      coroutineWrapper.status = 'error';
      console.error('协程启动失败:', error);
      throw error;
    }
  }
}

/**
 * 083 - 协程暂停节点
 * 协程让出执行权
 */
export class CoroutineYieldNode extends BaseNode {
  constructor() {
    super('CoroutineYield', '协程暂停', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('value', '让出值', DataType.ANY);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('yielded', '已让出', DataType.TRIGGER);
    this.addOutputPort('resumed', '已恢复', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInputValue('value');
    const delay = this.getInputValue('delay') || 0;
    
    // 触发让出事件
    await this.triggerOutput(context, 'yielded');
    
    // 如果有延迟，等待指定时间
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    // 在生成器函数中，这个节点实际上会通过 yield 关键字来实现
    // 这里我们模拟协程的暂停和恢复
    console.log(`协程暂停，让出值: ${value}`);
    
    // 触发恢复事件
    await this.triggerOutput(context, 'resumed');
    await this.triggerOutput(context, 'output');
  }
}

/**
 * 084 - 协程恢复节点
 * 恢复协程执行
 */
export class CoroutineResumeNode extends BaseNode {
  constructor() {
    super('CoroutineResume', '协程恢复', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('coroutine', '协程', DataType.OBJECT);
    this.addInputPort('value', '传入值', DataType.ANY);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('done', '完成', DataType.BOOLEAN);
    this.addOutputPort('resumed', '已恢复', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const coroutineWrapper = this.getInputValue('coroutine');
    const value = this.getInputValue('value');
    
    if (!coroutineWrapper || !coroutineWrapper.generator) {
      throw new Error('必须提供有效的协程对象');
    }
    
    const generator = coroutineWrapper.generator;
    
    if (coroutineWrapper.isDone) {
      console.warn('协程已完成，无法恢复');
      return;
    }
    
    try {
      // 恢复协程执行
      const result = generator.next(value);
      
      // 更新协程状态
      coroutineWrapper.status = result.done ? 'completed' : 'running';
      coroutineWrapper.lastYieldValue = result.value;
      coroutineWrapper.isDone = result.done;
      
      this.setOutputValue('result', result.value);
      this.setOutputValue('done', result.done);
      
      console.log(`协程已恢复: ${coroutineWrapper.id}`);
      await this.triggerOutput(context, 'resumed');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      coroutineWrapper.status = 'error';
      console.error('协程恢复失败:', error);
      throw error;
    }
  }
}

/**
 * 085 - 协程等待节点
 * 等待协程完成
 */
export class CoroutineWaitNode extends BaseNode {
  constructor() {
    super('CoroutineWait', '协程等待', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('coroutine', '协程', DataType.OBJECT);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('timeout', '超时', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const coroutineWrapper = this.getInputValue('coroutine');
    const timeout = this.getInputValue('timeout') || 10000;
    
    if (!coroutineWrapper || !coroutineWrapper.generator) {
      throw new Error('必须提供有效的协程对象');
    }
    
    const startTime = Date.now();
    
    try {
      // 等待协程完成
      while (!coroutineWrapper.isDone) {
        // 检查超时
        if (Date.now() - startTime > timeout) {
          console.warn(`协程等待超时: ${coroutineWrapper.id}`);
          await this.triggerOutput(context, 'timeout');
          return;
        }
        
        // 短暂等待后再次检查
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      this.setOutputValue('result', coroutineWrapper.lastYieldValue);
      
      console.log(`协程已完成: ${coroutineWrapper.id}`);
      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('协程等待失败:', error);
      throw error;
    }
  }
}

/**
 * 086 - 协程取消节点
 * 取消协程执行
 */
export class CoroutineCancelNode extends BaseNode {
  constructor() {
    super('CoroutineCancel', '协程取消', NodeCategory.LOGIC);
    
    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('coroutine', '协程', DataType.OBJECT);
    this.addInputPort('reason', '取消原因', DataType.STRING);
    
    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('cancelled', '已取消', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const coroutineWrapper = this.getInputValue('coroutine');
    const reason = this.getInputValue('reason') || '用户取消';
    
    if (!coroutineWrapper || !coroutineWrapper.generator) {
      throw new Error('必须提供有效的协程对象');
    }
    
    const generator = coroutineWrapper.generator;
    
    try {
      // 取消协程
      if (typeof generator.return === 'function') {
        generator.return(undefined);
      }
      
      // 更新协程状态
      coroutineWrapper.status = 'cancelled';
      coroutineWrapper.isDone = true;
      coroutineWrapper.cancelReason = reason;
      
      console.log(`协程已取消: ${coroutineWrapper.id}, 原因: ${reason}`);
      await this.triggerOutput(context, 'cancelled');
      await this.triggerOutput(context, 'output');
      
    } catch (error) {
      console.error('协程取消失败:', error);
      throw error;
    }
  }
}

/**
 * 087 - 异步函数节点
 * 异步函数定义和执行
 */
export class AsyncFunctionNode extends BaseNode {
  constructor() {
    super('AsyncFunction', '异步函数', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('asyncFunction', '异步函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('timeout', '超时', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const asyncFunction = this.getInputValue('asyncFunction');
    const args = this.getInputValue('arguments') || [];
    const timeout = this.getInputValue('timeout') || 30000;

    if (!asyncFunction || typeof asyncFunction !== 'function') {
      throw new Error('必须提供异步函数');
    }

    try {
      // 执行异步函数，带超时控制
      const result = await Promise.race([
        asyncFunction(...args),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('异步函数执行超时')), timeout)
        )
      ]);

      this.setOutputValue('result', result);
      this.setOutputValue('error', null);

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      if (error.message === '异步函数执行超时') {
        console.warn('异步函数执行超时');
        await this.triggerOutput(context, 'timeout');
      } else {
        console.error('异步函数执行失败:', error);

        this.setOutputValue('result', null);
        this.setOutputValue('error', {
          message: error.message,
          stack: error.stack,
          timestamp: Date.now()
        });

        throw error;
      }
    }
  }
}

/**
 * 088 - 等待异步节点
 * 等待异步操作完成
 */
export class AwaitNode extends BaseNode {
  constructor() {
    super('Await', '等待异步', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('promise', 'Promise', DataType.PROMISE);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('resolved', '已解决', DataType.TRIGGER);
    this.addOutputPort('rejected', '已拒绝', DataType.TRIGGER);
    this.addOutputPort('timeout', '超时', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const promise = this.getInputValue('promise');
    const timeout = this.getInputValue('timeout') || 10000;

    if (!promise || typeof promise.then !== 'function') {
      throw new Error('必须提供有效的Promise对象');
    }

    try {
      // 等待Promise解决，带超时控制
      const result = await Promise.race([
        promise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Promise等待超时')), timeout)
        )
      ]);

      this.setOutputValue('result', result);
      this.setOutputValue('error', null);

      await this.triggerOutput(context, 'resolved');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      if (error.message === 'Promise等待超时') {
        console.warn('Promise等待超时');
        await this.triggerOutput(context, 'timeout');
      } else {
        console.error('Promise被拒绝:', error);

        this.setOutputValue('result', null);
        this.setOutputValue('error', {
          message: error.message,
          stack: error.stack,
          timestamp: Date.now()
        });

        await this.triggerOutput(context, 'rejected');
        throw error;
      }
    }
  }
}

/**
 * 089 - Promise创建节点
 * 创建Promise
 */
export class PromiseCreateNode extends BaseNode {
  constructor() {
    super('PromiseCreate', 'Promise创建', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('executor', '执行器', DataType.FUNCTION);
    this.addInputPort('autoResolve', '自动解决', DataType.BOOLEAN);
    this.addInputPort('resolveValue', '解决值', DataType.ANY);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('promise', 'Promise', DataType.PROMISE);
    this.addOutputPort('created', '已创建', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const executor = this.getInputValue('executor');
    const autoResolve = this.getInputValue('autoResolve') || false;
    const resolveValue = this.getInputValue('resolveValue');
    const delay = this.getInputValue('delay') || 0;

    let promise: Promise<any>;

    if (executor && typeof executor === 'function') {
      // 使用自定义执行器
      promise = new Promise((resolve, reject) => {
        try {
          executor(resolve, reject, context);
        } catch (error) {
          reject(error);
        }
      });
    } else if (autoResolve) {
      // 自动解决的Promise
      promise = new Promise((resolve) => {
        setTimeout(() => {
          resolve(resolveValue);
        }, delay);
      });
    } else {
      // 创建一个永远不解决的Promise（用于测试）
      promise = new Promise(() => {});
    }

    this.setOutputValue('promise', promise);

    console.log('Promise已创建');
    await this.triggerOutput(context, 'created');
    await this.triggerOutput(context, 'output');
  }
}

/**
 * 090 - Promise解决节点
 * 解决Promise
 */
export class PromiseResolveNode extends BaseNode {
  constructor() {
    super('PromiseResolve', 'Promise解决', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('value', '解决值', DataType.ANY);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('promise', 'Promise', DataType.PROMISE);
    this.addOutputPort('resolved', '已解决', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const value = this.getInputValue('value');
    const delay = this.getInputValue('delay') || 0;

    // 创建立即解决的Promise
    const promise = delay > 0
      ? new Promise(resolve => setTimeout(() => resolve(value), delay))
      : Promise.resolve(value);

    this.setOutputValue('promise', promise);

    // 等待Promise解决
    await promise;

    console.log('Promise已解决，值:', value);
    await this.triggerOutput(context, 'resolved');
    await this.triggerOutput(context, 'output');
  }
}

/**
 * 091 - Promise拒绝节点
 * 拒绝Promise
 */
export class PromiseRejectNode extends BaseNode {
  constructor() {
    super('PromiseReject', 'Promise拒绝', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('reason', '拒绝原因', DataType.ANY);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('promise', 'Promise', DataType.PROMISE);
    this.addOutputPort('rejected', '已拒绝', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const reason = this.getInputValue('reason') || new Error('Promise被拒绝');
    const delay = this.getInputValue('delay') || 0;

    // 创建立即拒绝的Promise
    const promise = delay > 0
      ? new Promise((_, reject) => setTimeout(() => reject(reason), delay))
      : Promise.reject(reason);

    this.setOutputValue('promise', promise);

    try {
      await promise;
    } catch (error) {
      console.log('Promise已拒绝，原因:', error);
      await this.triggerOutput(context, 'rejected');
    }

    await this.triggerOutput(context, 'output');
  }
}

/**
 * 092 - Promise链式节点
 * Promise链式调用
 */
export class PromiseChainNode extends BaseNode {
  constructor() {
    super('PromiseChain', 'Promise链式', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('initialPromise', '初始Promise', DataType.PROMISE);
    this.addInputPort('chainFunctions', '链式函数', DataType.ARRAY);
    this.addInputPort('catchHandler', '错误处理器', DataType.FUNCTION);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const initialPromise = this.getInputValue('initialPromise');
    const chainFunctions = this.getInputValue('chainFunctions') || [];
    const catchHandler = this.getInputValue('catchHandler');

    if (!initialPromise || typeof initialPromise.then !== 'function') {
      throw new Error('必须提供初始Promise');
    }

    if (!Array.isArray(chainFunctions)) {
      throw new Error('链式函数必须是数组');
    }

    try {
      // 构建Promise链
      let promiseChain = initialPromise;

      for (const func of chainFunctions) {
        if (typeof func !== 'function') {
          throw new Error('链式函数数组中包含非函数元素');
        }

        promiseChain = promiseChain.then(func);
      }

      // 添加错误处理
      if (catchHandler && typeof catchHandler === 'function') {
        promiseChain = promiseChain.catch(catchHandler);
      }

      // 等待链式执行完成
      const result = await promiseChain;

      this.setOutputValue('result', result);
      this.setOutputValue('error', null);

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('Promise链式执行失败:', error);

      this.setOutputValue('result', null);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });

      throw error;
    }
  }
}

/**
 * 093 - Promise并行节点
 * 并行执行多个Promise
 */
export class PromiseAllNode extends BaseNode {
  constructor() {
    super('PromiseAll', 'Promise并行', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('promises', 'Promise列表', DataType.ARRAY);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);
    this.addInputPort('failFast', '快速失败', DataType.BOOLEAN);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('results', '结果列表', DataType.ARRAY);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('timeout', '超时', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const promises = this.getInputValue('promises') || [];
    const timeout = this.getInputValue('timeout') || 30000;
    const failFast = this.getInputValue('failFast') !== false;

    if (!Array.isArray(promises) || promises.length === 0) {
      throw new Error('必须提供Promise数组');
    }

    // 验证所有元素都是Promise
    for (const promise of promises) {
      if (!promise || typeof promise.then !== 'function') {
        throw new Error('Promise列表中包含非Promise元素');
      }
    }

    try {
      let resultPromise: Promise<any>;

      if (failFast) {
        // 使用Promise.all，任何一个失败都会导致整体失败
        resultPromise = Promise.all(promises);
      } else {
        // 使用Promise.allSettled，等待所有Promise完成
        resultPromise = Promise.allSettled(promises);
      }

      // 添加超时控制
      const results = await Promise.race([
        resultPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Promise并行执行超时')), timeout)
        )
      ]);

      this.setOutputValue('results', results);
      this.setOutputValue('error', null);

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      if (error.message === 'Promise并行执行超时') {
        console.warn('Promise并行执行超时');
        await this.triggerOutput(context, 'timeout');
      } else {
        console.error('Promise并行执行失败:', error);

        this.setOutputValue('results', null);
        this.setOutputValue('error', {
          message: error.message,
          stack: error.stack,
          timestamp: Date.now()
        });

        throw error;
      }
    }
  }
}

/**
 * 094 - Promise竞争节点
 * Promise竞争执行
 */
export class PromiseRaceNode extends BaseNode {
  constructor() {
    super('PromiseRace', 'Promise竞争', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('promises', 'Promise列表', DataType.ARRAY);
    this.addInputPort('timeout', '超时时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('winnerIndex', '获胜索引', DataType.NUMBER);
    this.addOutputPort('error', '错误', DataType.OBJECT);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const promises = this.getInputValue('promises') || [];
    const timeout = this.getInputValue('timeout') || 10000;

    if (!Array.isArray(promises) || promises.length === 0) {
      throw new Error('必须提供Promise数组');
    }

    // 验证所有元素都是Promise
    for (const promise of promises) {
      if (!promise || typeof promise.then !== 'function') {
        throw new Error('Promise列表中包含非Promise元素');
      }
    }

    try {
      // 为每个Promise添加索引信息
      const indexedPromises = promises.map((promise, index) =>
        promise.then(
          (value: any) => ({ value, index, status: 'fulfilled' }),
          (reason: any) => ({ reason, index, status: 'rejected' })
        )
      );

      // 添加超时Promise
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Promise竞争超时')), timeout)
      );

      // 执行竞争
      const result = await Promise.race([...indexedPromises, timeoutPromise]);

      if (result.status === 'fulfilled') {
        this.setOutputValue('result', result.value);
        this.setOutputValue('winnerIndex', result.index);
        this.setOutputValue('error', null);
      } else {
        throw new Error(`Promise ${result.index} 被拒绝: ${result.reason}`);
      }

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('Promise竞争失败:', error);

      this.setOutputValue('result', null);
      this.setOutputValue('winnerIndex', -1);
      this.setOutputValue('error', {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });

      throw error;
    }
  }
}

/**
 * 095 - 异步迭代器节点
 * 异步迭代器
 */
export class AsyncIteratorNode extends BaseNode {
  constructor() {
    super('AsyncIterator', '异步迭代器', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('asyncIterable', '异步可迭代对象', DataType.OBJECT);
    this.addInputPort('processor', '处理器函数', DataType.FUNCTION);
    this.addInputPort('batchSize', '批次大小', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('results', '结果列表', DataType.ARRAY);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('progress', '进度', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const asyncIterable = this.getInputValue('asyncIterable');
    const processor = this.getInputValue('processor');
    const batchSize = this.getInputValue('batchSize') || 10;

    if (!asyncIterable || typeof asyncIterable[Symbol.asyncIterator] !== 'function') {
      throw new Error('必须提供异步可迭代对象');
    }

    const results: any[] = [];
    let processedCount = 0;
    let batch: any[] = [];

    try {
      // 遍历异步迭代器
      for await (const item of asyncIterable) {
        batch.push(item);

        // 当批次满了或者是最后一个项目时处理批次
        if (batch.length >= batchSize) {
          await this.processBatch(batch, processor, results, context);
          processedCount += batch.length;
          batch = [];

          // 更新进度
          this.setOutputValue('progress', processedCount);
        }
      }

      // 处理剩余的批次
      if (batch.length > 0) {
        await this.processBatch(batch, processor, results, context);
        processedCount += batch.length;
      }

      this.setOutputValue('results', results);
      this.setOutputValue('progress', processedCount);

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('异步迭代器处理失败:', error);
      throw error;
    }
  }

  private async processBatch(
    batch: any[],
    processor: Function | undefined,
    results: any[],
    context: IExecutionContext
  ): Promise<void> {
    if (processor && typeof processor === 'function') {
      // 使用自定义处理器
      for (const item of batch) {
        const result = await processor(item, context);
        results.push(result);
      }
    } else {
      // 直接添加到结果
      results.push(...batch);
    }
  }
}

/**
 * 096 - 异步生成器节点
 * 异步生成器
 */
export class AsyncGeneratorNode extends BaseNode {
  constructor() {
    super('AsyncGenerator', '异步生成器', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('generatorFunction', '生成器函数', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('maxItems', '最大项目数', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('asyncIterable', '异步可迭代对象', DataType.OBJECT);
    this.addOutputPort('items', '项目列表', DataType.ARRAY);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const generatorFunction = this.getInputValue('generatorFunction');
    const args = this.getInputValue('arguments') || [];
    const maxItems = this.getInputValue('maxItems') || 100;

    if (!generatorFunction || typeof generatorFunction !== 'function') {
      throw new Error('必须提供异步生成器函数');
    }

    try {
      // 创建异步生成器
      const asyncGenerator = generatorFunction(...args);

      if (!asyncGenerator || typeof asyncGenerator[Symbol.asyncIterator] !== 'function') {
        throw new Error('提供的函数不是异步生成器函数');
      }

      this.setOutputValue('asyncIterable', asyncGenerator);

      // 收集生成的项目
      const items: any[] = [];
      let count = 0;

      for await (const item of asyncGenerator) {
        items.push(item);
        count++;

        // 防止无限生成
        if (count >= maxItems) {
          console.warn(`异步生成器达到最大项目数限制: ${maxItems}`);
          break;
        }
      }

      this.setOutputValue('items', items);

      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('异步生成器执行失败:', error);
      throw error;
    }
  }
}

/**
 * 097 - 任务队列节点
 * 任务队列管理
 */
export class TaskQueueNode extends BaseNode {
  private taskQueue: Array<{ id: string; task: Function; priority: number }> = [];
  private isProcessing = false;

  constructor() {
    super('TaskQueue', '任务队列', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('task', '任务', DataType.FUNCTION);
    this.addInputPort('priority', '优先级', DataType.NUMBER);
    this.addInputPort('taskId', '任务ID', DataType.STRING);
    this.addInputPort('concurrency', '并发数', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('taskAdded', '任务已添加', DataType.TRIGGER);
    this.addOutputPort('taskCompleted', '任务已完成', DataType.TRIGGER);
    this.addOutputPort('queueEmpty', '队列为空', DataType.TRIGGER);
    this.addOutputPort('queueSize', '队列大小', DataType.NUMBER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const task = this.getInputValue('task');
    const priority = this.getInputValue('priority') || 0;
    const taskId = this.getInputValue('taskId') || `task_${Date.now()}`;
    const concurrency = this.getInputValue('concurrency') || 1;

    if (task && typeof task === 'function') {
      // 添加任务到队列
      this.addTask(taskId, task, priority);
      await this.triggerOutput(context, 'taskAdded');
    }

    // 处理队列
    if (!this.isProcessing) {
      this.processQueue(context, concurrency);
    }

    this.setOutputValue('queueSize', this.taskQueue.length);
    await this.triggerOutput(context, 'output');
  }

  private addTask(id: string, task: Function, priority: number): void {
    this.taskQueue.push({ id, task, priority });

    // 按优先级排序（高优先级在前）
    this.taskQueue.sort((a, b) => b.priority - a.priority);

    console.log(`任务已添加到队列: ${id}, 优先级: ${priority}`);
  }

  private async processQueue(context: IExecutionContext, concurrency: number): Promise<void> {
    this.isProcessing = true;

    try {
      while (this.taskQueue.length > 0) {
        // 获取要并发执行的任务
        const tasksToProcess = this.taskQueue.splice(0, concurrency);

        // 并发执行任务
        const taskPromises = tasksToProcess.map(async ({ id, task }) => {
          try {
            console.log(`开始执行任务: ${id}`);
            await task(context);
            console.log(`任务完成: ${id}`);
            await this.triggerOutput(context, 'taskCompleted');
          } catch (error) {
            console.error(`任务执行失败: ${id}`, error);
          }
        });

        await Promise.all(taskPromises);

        // 更新队列大小
        this.setOutputValue('queueSize', this.taskQueue.length);
      }

      // 队列为空
      await this.triggerOutput(context, 'queueEmpty');

    } finally {
      this.isProcessing = false;
    }
  }

  // 清空队列
  clearQueue(): void {
    this.taskQueue = [];
  }

  // 获取队列状态
  getQueueStatus(): { size: number; isProcessing: boolean } {
    return {
      size: this.taskQueue.length,
      isProcessing: this.isProcessing
    };
  }
}

/**
 * 098 - 事件循环节点
 * 事件循环控制
 */
export class EventLoopNode extends BaseNode {
  private eventQueue: Array<{ event: string; data: any; timestamp: number }> = [];
  private listeners: Map<string, Function[]> = new Map();
  private isRunning = false;

  constructor() {
    super('EventLoop', '事件循环', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('event', '事件', DataType.STRING);
    this.addInputPort('eventData', '事件数据', DataType.ANY);
    this.addInputPort('listener', '监听器', DataType.FUNCTION);
    this.addInputPort('eventType', '事件类型', DataType.STRING);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('eventEmitted', '事件已发出', DataType.TRIGGER);
    this.addOutputPort('eventProcessed', '事件已处理', DataType.TRIGGER);
    this.addOutputPort('loopStarted', '循环已启动', DataType.TRIGGER);
    this.addOutputPort('loopStopped', '循环已停止', DataType.TRIGGER);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const event = this.getInputValue('event');
    const eventData = this.getInputValue('eventData');
    const listener = this.getInputValue('listener');
    const eventType = this.getInputValue('eventType');

    // 添加事件监听器
    if (listener && typeof listener === 'function' && eventType) {
      this.addEventListener(eventType, listener);
    }

    // 发出事件
    if (event) {
      this.emitEvent(event, eventData);
      await this.triggerOutput(context, 'eventEmitted');
    }

    // 启动事件循环
    if (!this.isRunning) {
      this.startEventLoop(context);
      await this.triggerOutput(context, 'loopStarted');
    }

    await this.triggerOutput(context, 'output');
  }

  private addEventListener(eventType: string, listener: Function): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }

    this.listeners.get(eventType)!.push(listener);
    console.log(`事件监听器已添加: ${eventType}`);
  }

  private emitEvent(event: string, data: any): void {
    this.eventQueue.push({
      event,
      data,
      timestamp: Date.now()
    });

    console.log(`事件已发出: ${event}`);
  }

  private async startEventLoop(context: IExecutionContext): Promise<void> {
    this.isRunning = true;

    // 事件循环
    while (this.isRunning) {
      if (this.eventQueue.length > 0) {
        const { event, data } = this.eventQueue.shift()!;

        // 处理事件
        const listeners = this.listeners.get(event) || [];

        for (const listener of listeners) {
          try {
            await listener(data, context);
          } catch (error) {
            console.error(`事件监听器执行失败: ${event}`, error);
          }
        }

        await this.triggerOutput(context, 'eventProcessed');
      } else {
        // 没有事件时短暂等待
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    await this.triggerOutput(context, 'loopStopped');
  }

  // 停止事件循环
  stopEventLoop(): void {
    this.isRunning = false;
  }

  // 移除事件监听器
  removeEventListener(eventType: string, listener: Function): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  dispose(): void {
    this.stopEventLoop();
    this.eventQueue = [];
    this.listeners.clear();
    super.dispose();
  }
}

/**
 * 099 - 微任务节点
 * 微任务调度
 */
export class MicrotaskNode extends BaseNode {
  constructor() {
    super('Microtask', '微任务', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('task', '任务', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('scheduled', '已调度', DataType.TRIGGER);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
  }

  async execute(context: IExecutionContext): Promise<void> {
    const task = this.getInputValue('task');
    const args = this.getInputValue('arguments') || [];

    if (!task || typeof task !== 'function') {
      throw new Error('必须提供任务函数');
    }

    // 调度微任务
    await this.triggerOutput(context, 'scheduled');

    // 使用queueMicrotask或Promise.resolve来调度微任务
    const microtaskPromise = new Promise<any>((resolve, reject) => {
      if (typeof queueMicrotask !== 'undefined') {
        queueMicrotask(async () => {
          try {
            const result = await task(...args, context);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      } else {
        // 回退到Promise.resolve
        Promise.resolve().then(async () => {
          try {
            const result = await task(...args, context);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      }
    });

    try {
      const result = await microtaskPromise;

      this.setOutputValue('result', result);

      console.log('微任务已完成');
      await this.triggerOutput(context, 'completed');
      await this.triggerOutput(context, 'output');

    } catch (error) {
      console.error('微任务执行失败:', error);
      throw error;
    }
  }
}

/**
 * 100 - 宏任务节点
 * 宏任务调度
 */
export class MacrotaskNode extends BaseNode {
  constructor() {
    super('Macrotask', '宏任务', NodeCategory.LOGIC);

    this.addInputPort('input', '输入', DataType.TRIGGER);
    this.addInputPort('task', '任务', DataType.FUNCTION);
    this.addInputPort('arguments', '参数', DataType.ARRAY);
    this.addInputPort('delay', '延迟时间', DataType.NUMBER);
    this.addInputPort('interval', '间隔时间', DataType.NUMBER);

    this.addOutputPort('output', '输出', DataType.TRIGGER);
    this.addOutputPort('scheduled', '已调度', DataType.TRIGGER);
    this.addOutputPort('completed', '已完成', DataType.TRIGGER);
    this.addOutputPort('result', '结果', DataType.ANY);
    this.addOutputPort('cancelled', '已取消', DataType.TRIGGER);
  }

  private timerId: NodeJS.Timeout | null = null;

  async execute(context: IExecutionContext): Promise<void> {
    const task = this.getInputValue('task');
    const args = this.getInputValue('arguments') || [];
    const delay = this.getInputValue('delay') || 0;
    const interval = this.getInputValue('interval');

    if (!task || typeof task !== 'function') {
      throw new Error('必须提供任务函数');
    }

    // 取消之前的定时器
    if (this.timerId) {
      clearTimeout(this.timerId);
      clearInterval(this.timerId);
    }

    await this.triggerOutput(context, 'scheduled');

    const executeTask = async () => {
      try {
        const result = await task(...args, context);
        this.setOutputValue('result', result);

        console.log('宏任务已完成');
        await this.triggerOutput(context, 'completed');

        return result;
      } catch (error) {
        console.error('宏任务执行失败:', error);
        throw error;
      }
    };

    if (interval && interval > 0) {
      // 间隔执行
      this.timerId = setInterval(executeTask, interval);
    } else {
      // 延迟执行一次
      this.timerId = setTimeout(executeTask, delay);
    }

    await this.triggerOutput(context, 'output');
  }

  // 取消宏任务
  cancel(): void {
    if (this.timerId) {
      clearTimeout(this.timerId);
      clearInterval(this.timerId);
      this.timerId = null;

      console.log('宏任务已取消');
    }
  }

  dispose(): void {
    this.cancel();
    super.dispose();
  }
}
