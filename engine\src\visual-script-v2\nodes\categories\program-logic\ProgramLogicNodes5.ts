/**
 * 第一批次：程序逻辑控制基础节点 (037-050)
 * 完成最后的设计模式和高级控制节点
 */

import { IVisualScriptNode, IExecutionContext, NodeCategory, DataType, NodePort, NodeProperty } from '../../../core/types';
import { BaseNode } from '../../base/BaseNode';

/**
 * 037 - 迭代器 (Iterator)
 * 迭代器模式实现
 */
export class IteratorNode extends BaseNode {
  private items: any[] = [];
  private currentIndex = 0;

  constructor(id?: string) {
    super(id, 'program-logic/iterator', '迭代器', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'setItems',
        label: '设置项目',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '设置要迭代的项目'
      },
      {
        name: 'items',
        label: '项目列表',
        type: DataType.ARRAY,
        direction: 'input',
        required: false,
        description: '要迭代的项目数组'
      },
      {
        name: 'next',
        label: '下一个',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '移动到下一个项目'
      },
      {
        name: 'previous',
        label: '上一个',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '移动到上一个项目'
      },
      {
        name: 'reset',
        label: '重置',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重置到开始位置'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'currentItem',
        label: '当前项目',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '当前迭代的项目'
      },
      {
        name: 'index',
        label: '索引',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前索引'
      },
      {
        name: 'hasNext',
        label: '有下一个',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否有下一个项目'
      },
      {
        name: 'hasPrevious',
        label: '有上一个',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否有上一个项目'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const setItemsTrigger = context.getInputValue('setItems');
    const items = context.getInputValue('items');
    const nextTrigger = context.getInputValue('next');
    const previousTrigger = context.getInputValue('previous');
    const resetTrigger = context.getInputValue('reset');

    if (setItemsTrigger && Array.isArray(items)) {
      this.items = [...items];
      this.currentIndex = 0;
    }

    if (resetTrigger) {
      this.currentIndex = 0;
    }

    if (nextTrigger && this.currentIndex < this.items.length - 1) {
      this.currentIndex++;
    }

    if (previousTrigger && this.currentIndex > 0) {
      this.currentIndex--;
    }

    if (this.items.length > 0 && this.currentIndex < this.items.length) {
      context.setOutputValue('currentItem', this.items[this.currentIndex]);
    }

    context.setOutputValue('index', this.currentIndex);
    context.setOutputValue('hasNext', this.currentIndex < this.items.length - 1);
    context.setOutputValue('hasPrevious', this.currentIndex > 0);
  }
}

/**
 * 038 - 观察者模式 (Observer Pattern)
 * 观察者模式实现
 */
export class ObserverPatternNode extends BaseNode {
  private observers: Array<{ id: string; callback: any }> = [];
  private state: any = null;

  constructor(id?: string) {
    super(id, 'program-logic/observer-pattern', '观察者模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'subscribe',
        label: '订阅',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '添加观察者'
      },
      {
        name: 'unsubscribe',
        label: '取消订阅',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '移除观察者'
      },
      {
        name: 'notify',
        label: '通知',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '通知所有观察者'
      },
      {
        name: 'setState',
        label: '设置状态',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '设置新状态'
      },
      {
        name: 'observerId',
        label: '观察者ID',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '观察者标识'
      },
      {
        name: 'newState',
        label: '新状态',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '新的状态值'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'notifyObserver',
        label: '通知观察者',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '通知观察者状态变化'
      },
      {
        name: 'state',
        label: '状态',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '当前状态'
      },
      {
        name: 'observerCount',
        label: '观察者数量',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '观察者数量'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const subscribeTrigger = context.getInputValue('subscribe');
    const unsubscribeTrigger = context.getInputValue('unsubscribe');
    const notifyTrigger = context.getInputValue('notify');
    const setStateTrigger = context.getInputValue('setState');
    const observerId = context.getInputValue('observerId');
    const newState = context.getInputValue('newState');

    if (subscribeTrigger && observerId) {
      if (!this.observers.find(obs => obs.id === observerId)) {
        this.observers.push({ id: observerId, callback: null });
      }
    }

    if (unsubscribeTrigger && observerId) {
      this.observers = this.observers.filter(obs => obs.id !== observerId);
    }

    if (setStateTrigger) {
      this.state = newState;
      // 自动通知所有观察者
      context.setOutputValue('notifyObserver', true);
    }

    if (notifyTrigger) {
      context.setOutputValue('notifyObserver', true);
    }

    context.setOutputValue('state', this.state);
    context.setOutputValue('observerCount', this.observers.length);
  }
}

/**
 * 039 - 命令模式 (Command Pattern)
 * 命令模式实现
 */
export class CommandPatternNode extends BaseNode {
  private commandHistory: Array<{ name: string; data: any; timestamp: number }> = [];
  private currentIndex = -1;

  constructor(id?: string) {
    super(id, 'program-logic/command-pattern', '命令模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'execute',
        label: '执行命令',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '执行新命令'
      },
      {
        name: 'undo',
        label: '撤销',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '撤销上一个命令'
      },
      {
        name: 'redo',
        label: '重做',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '重做下一个命令'
      },
      {
        name: 'clear',
        label: '清空历史',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '清空命令历史'
      },
      {
        name: 'commandName',
        label: '命令名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '要执行的命令名'
      },
      {
        name: 'commandData',
        label: '命令数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '命令参数数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'executeCommand',
        label: '执行命令',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '执行命令输出'
      },
      {
        name: 'undoCommand',
        label: '撤销命令',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '撤销命令输出'
      },
      {
        name: 'redoCommand',
        label: '重做命令',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '重做命令输出'
      },
      {
        name: 'currentCommand',
        label: '当前命令',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前命令名'
      },
      {
        name: 'commandData',
        label: '命令数据',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '当前命令数据'
      },
      {
        name: 'canUndo',
        label: '可撤销',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否可以撤销'
      },
      {
        name: 'canRedo',
        label: '可重做',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否可以重做'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const executeTrigger = context.getInputValue('execute');
    const undoTrigger = context.getInputValue('undo');
    const redoTrigger = context.getInputValue('redo');
    const clearTrigger = context.getInputValue('clear');
    const commandName = context.getInputValue('commandName');
    const commandData = context.getInputValue('commandData');

    if (clearTrigger) {
      this.commandHistory = [];
      this.currentIndex = -1;
    }

    if (executeTrigger && commandName) {
      // 添加新命令到历史
      const command = {
        name: commandName,
        data: commandData,
        timestamp: Date.now()
      };
      
      // 如果当前不在历史末尾，删除后续历史
      if (this.currentIndex < this.commandHistory.length - 1) {
        this.commandHistory = this.commandHistory.slice(0, this.currentIndex + 1);
      }
      
      this.commandHistory.push(command);
      this.currentIndex = this.commandHistory.length - 1;
      
      context.setOutputValue('executeCommand', true);
      context.setOutputValue('currentCommand', commandName);
      context.setOutputValue('commandData', commandData);
    }

    if (undoTrigger && this.currentIndex >= 0) {
      const command = this.commandHistory[this.currentIndex];
      this.currentIndex--;
      
      context.setOutputValue('undoCommand', true);
      context.setOutputValue('currentCommand', command.name);
      context.setOutputValue('commandData', command.data);
    }

    if (redoTrigger && this.currentIndex < this.commandHistory.length - 1) {
      this.currentIndex++;
      const command = this.commandHistory[this.currentIndex];
      
      context.setOutputValue('redoCommand', true);
      context.setOutputValue('currentCommand', command.name);
      context.setOutputValue('commandData', command.data);
    }

    context.setOutputValue('canUndo', this.currentIndex >= 0);
    context.setOutputValue('canRedo', this.currentIndex < this.commandHistory.length - 1);
  }
}

/**
 * 040 - 策略模式 (Strategy Pattern)
 * 策略模式实现
 */
export class StrategyPatternNode extends BaseNode {
  private currentStrategy = 'default';

  constructor(id?: string) {
    super(id, 'program-logic/strategy-pattern', '策略模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'execute',
        label: '执行',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '执行当前策略'
      },
      {
        name: 'setStrategy',
        label: '设置策略',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '设置执行策略'
      },
      {
        name: 'strategyName',
        label: '策略名',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        description: '策略名称'
      },
      {
        name: 'input',
        label: '输入数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '策略处理的输入数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'strategyA',
        label: '策略A',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '执行策略A'
      },
      {
        name: 'strategyB',
        label: '策略B',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '执行策略B'
      },
      {
        name: 'strategyC',
        label: '策略C',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '执行策略C'
      },
      {
        name: 'currentStrategy',
        label: '当前策略',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '当前使用的策略'
      },
      {
        name: 'input',
        label: '输入数据',
        type: DataType.ANY,
        direction: 'output',
        required: false,
        description: '传递给策略的输入数据'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const executeTrigger = context.getInputValue('execute');
    const setStrategyTrigger = context.getInputValue('setStrategy');
    const strategyName = context.getInputValue('strategyName');
    const input = context.getInputValue('input');

    if (setStrategyTrigger && strategyName) {
      this.currentStrategy = strategyName;
    }

    context.setOutputValue('currentStrategy', this.currentStrategy);
    context.setOutputValue('input', input);

    if (executeTrigger) {
      switch (this.currentStrategy) {
        case 'A':
        case 'strategyA':
          context.setOutputValue('strategyA', true);
          break;
        case 'B':
        case 'strategyB':
          context.setOutputValue('strategyB', true);
          break;
        case 'C':
        case 'strategyC':
          context.setOutputValue('strategyC', true);
          break;
        default:
          context.setOutputValue('strategyA', true); // 默认策略
          break;
      }
    }
  }
}

/**
 * 041 - 工厂模式 (Factory Pattern)
 * 工厂模式实现
 */
export class FactoryPatternNode extends BaseNode {
  constructor(id?: string) {
    super(id, 'program-logic/factory-pattern', '工厂模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'create',
        label: '创建',
        type: DataType.TRIGGER,
        direction: 'input',
        required: true,
        description: '创建对象'
      },
      {
        name: 'objectType',
        label: '对象类型',
        type: DataType.STRING,
        direction: 'input',
        required: true,
        description: '要创建的对象类型'
      },
      {
        name: 'parameters',
        label: '参数',
        type: DataType.OBJECT,
        direction: 'input',
        required: false,
        description: '创建对象的参数'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'created',
        label: '已创建',
        type: DataType.TRIGGER,
        direction: 'output',
        required: false,
        description: '对象创建完成'
      },
      {
        name: 'object',
        label: '对象',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '创建的对象'
      },
      {
        name: 'objectType',
        label: '对象类型',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '创建的对象类型'
      },
      {
        name: 'error',
        label: '错误',
        type: DataType.STRING,
        direction: 'output',
        required: false,
        description: '创建失败的错误信息'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const objectType = context.getInputValue('objectType');
    const parameters = context.getInputValue('parameters') || {};

    try {
      // 模拟工厂创建对象
      const createdObject = {
        type: objectType,
        id: `${objectType}_${Date.now()}`,
        parameters: parameters,
        createdAt: new Date().toISOString()
      };

      context.setOutputValue('object', createdObject);
      context.setOutputValue('objectType', objectType);
      context.setOutputValue('created', true);

      context.log('info', `工厂创建对象: ${objectType}`, createdObject);
    } catch (error) {
      context.setOutputValue('error', error instanceof Error ? error.message : String(error));
    }
  }
}

/**
 * 042 - 单例模式 (Singleton Pattern)
 * 单例模式实现
 */
export class SingletonPatternNode extends BaseNode {
  private static instances = new Map<string, any>();

  constructor(id?: string) {
    super(id, 'program-logic/singleton-pattern', '单例模式', NodeCategory.FLOW_CONTROL);
  }

  getInputPorts(): NodePort[] {
    return [
      {
        name: 'getInstance',
        label: '获取实例',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '获取单例实例'
      },
      {
        name: 'destroyInstance',
        label: '销毁实例',
        type: DataType.TRIGGER,
        direction: 'input',
        required: false,
        description: '销毁单例实例'
      },
      {
        name: 'instanceKey',
        label: '实例键',
        type: DataType.STRING,
        direction: 'input',
        required: false,
        defaultValue: 'default',
        description: '单例实例的键'
      },
      {
        name: 'initData',
        label: '初始化数据',
        type: DataType.ANY,
        direction: 'input',
        required: false,
        description: '实例初始化数据'
      }
    ];
  }

  getOutputPorts(): NodePort[] {
    return [
      {
        name: 'instance',
        label: '实例',
        type: DataType.OBJECT,
        direction: 'output',
        required: false,
        description: '单例实例'
      },
      {
        name: 'isNewInstance',
        label: '是新实例',
        type: DataType.BOOLEAN,
        direction: 'output',
        required: false,
        description: '是否是新创建的实例'
      },
      {
        name: 'instanceCount',
        label: '实例数量',
        type: DataType.NUMBER,
        direction: 'output',
        required: false,
        description: '当前实例总数'
      }
    ];
  }

  async execute(context: IExecutionContext): Promise<void> {
    const getInstanceTrigger = context.getInputValue('getInstance');
    const destroyInstanceTrigger = context.getInputValue('destroyInstance');
    const instanceKey = context.getInputValue('instanceKey') || 'default';
    const initData = context.getInputValue('initData');

    if (destroyInstanceTrigger) {
      SingletonPatternNode.instances.delete(instanceKey);
      context.log('info', `销毁单例实例: ${instanceKey}`);
    }

    if (getInstanceTrigger) {
      let instance = SingletonPatternNode.instances.get(instanceKey);
      let isNewInstance = false;

      if (!instance) {
        // 创建新实例
        instance = {
          key: instanceKey,
          id: `singleton_${instanceKey}_${Date.now()}`,
          data: initData,
          createdAt: new Date().toISOString()
        };
        SingletonPatternNode.instances.set(instanceKey, instance);
        isNewInstance = true;
        context.log('info', `创建新单例实例: ${instanceKey}`, instance);
      }

      context.setOutputValue('instance', instance);
      context.setOutputValue('isNewInstance', isNewInstance);
    }

    context.setOutputValue('instanceCount', SingletonPatternNode.instances.size);
  }
}
